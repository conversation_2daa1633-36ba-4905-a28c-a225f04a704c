<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors to avoid interfering with JSON

// Check if this is an AJAX request first
$is_ajax_request = isset($_POST['ajax']) && $_POST['ajax'];

// Start output buffering only for non-AJAX requests
if (!$is_ajax_request) {
    ob_start();
}

// Include session management
include '../includes/session.php';

// Check if database connection has error
if (isset($db_error) && $db_error) {
    if ($is_ajax_request) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'Database connection error occurred. Please try again later.']);
        exit();
    }
    $_SESSION['error'] = 'Database connection error occurred. Please try again later.';
    header('location: ../index.php');
    exit();
}

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] != 'Admin') {
    if ($is_ajax_request) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'You do not have permission to access this page.']);
        exit();
    }
    $_SESSION['error'] = 'You do not have permission to access this page.';
    header('location: ../index.php');
    exit();
}

// Get all roles - moved to top for AJAX processing
try {
    // Get user types from users table, excluding Admin (Admin has all permissions by default)
    $stmt = $conn->query("SELECT DISTINCT user_type FROM users WHERE user_type IS NOT NULL AND user_type != 'Admin' ORDER BY user_type");
    $user_types = $stmt->fetchAll(PDO::FETCH_COLUMN);

    // Convert to roles format for compatibility
    $roles = [];
    foreach ($user_types as $index => $user_type) {
        $roles[] = [
            'role_id' => $index + 1,
            'role_name' => $user_type
        ];
    }

    // If no user types found, use default ones (excluding Admin)
    if (empty($roles)) {
        $default_user_types = ['Secretary', 'Chairman', 'Kagawad', 'Staff'];
        foreach ($default_user_types as $index => $user_type) {
            $roles[] = [
                'role_id' => $index + 1,
                'role_name' => $user_type
            ];
        }
    }
} catch (PDOException $e) {
    $roles = [];
    if ($is_ajax_request) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'Error fetching roles: ' . $e->getMessage()]);
        exit();
    }
    $_SESSION['error'] = "Error fetching roles: " . $e->getMessage();
}

// Get selected user type from URL parameter
$selected_user_type = isset($_GET['user_type']) ? $_GET['user_type'] : 'Secretary';

// Process permission actions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Add new permission
    if (isset($_POST['add_permission'])) {
        // Validate required fields
        if (empty($_POST['user_type']) || empty($_POST['module']) || empty($_POST['permission_name'])) {
            $error_msg = "All fields are required.";
            if ($is_ajax_request) {
                header('Content-Type: application/json');
                if (!$is_ajax_request) ob_end_clean();
                echo json_encode(['success' => false, 'message' => $error_msg]);
                exit();
            } else {
                $_SESSION['error'] = $error_msg;
            }
        }

        $user_type = trim($_POST['user_type']);
        $module = trim($_POST['module']);
        $permission_name = trim($_POST['permission_name']);
        $description = trim($_POST['description']);

        try {
            // Check if permissions table exists
            $checkTable = $conn->prepare("SHOW TABLES LIKE 'permissions'");
            $checkTable->execute();

            if ($checkTable->rowCount() == 0) {
                // Create permissions table with correct schema
                $conn->exec("CREATE TABLE permissions (
                    permission_id INT AUTO_INCREMENT PRIMARY KEY,
                    role_id INT NOT NULL,
                    module VARCHAR(50) NOT NULL,
                    permission_name VARCHAR(100) NOT NULL,
                    description TEXT,
                    status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_permission (role_id, module, permission_name)
                )");

                // Log the creation
                error_log('Created permissions table');
            }

            // Check if user_permissions table exists
            $checkTable = $conn->prepare("SHOW TABLES LIKE 'user_permissions'");
            $checkTable->execute();

            if ($checkTable->rowCount() == 0) {
                // Create user_permissions table
                $conn->exec("CREATE TABLE user_permissions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    role_id INT NOT NULL,
                    permission_id INT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_role_permission (role_id, permission_id)
                )");

                // Log the creation
                error_log('Created user_permissions table');
            }

            // Find the role_id for the selected user_type
            $role_id = null;
            foreach ($roles as $role) {
                if ($role['role_name'] == $user_type) {
                    $role_id = $role['role_id'];
                    break;
                }
            }

            if (!$role_id) {
                $error_msg = "Invalid user type selected: " . $user_type;
                if ($is_ajax_request) {
                    header('Content-Type: application/json');
                    echo json_encode(['success' => false, 'message' => $error_msg]);
                    exit();
                } else {
                    $_SESSION['error'] = $error_msg;
                }
            } else {
                // Check if permission already exists
                $check_stmt = $conn->prepare("SELECT permission_id FROM permissions WHERE role_id = ? AND module = ? AND permission_name = ?");
                $check_stmt->execute([$role_id, $module, $permission_name]);

                if ($check_stmt->rowCount() > 0) {
                    $error_msg = "This permission already exists for the selected user type and module.";
                    if ($is_ajax_request) {
                        header('Content-Type: application/json');
                        echo json_encode(['success' => false, 'message' => $error_msg]);
                        exit();
                    } else {
                        $_SESSION['error'] = $error_msg;
                    }
                } else {
                    // Insert permission with role_id
                    $stmt = $conn->prepare("INSERT INTO permissions (role_id, module, permission_name, description) VALUES (?, ?, ?, ?)");
                    $result = $stmt->execute([$role_id, $module, $permission_name, $description]);

                    if ($result) {
                        // Get the newly inserted permission ID
                        $new_permission_id = $conn->lastInsertId();

                        // Automatically assign this permission to the selected user type
                        $assign_stmt = $conn->prepare("INSERT INTO user_permissions (role_id, permission_id) VALUES (?, ?)");
                        $assign_result = $assign_stmt->execute([$role_id, $new_permission_id]);

                        if ($assign_result) {
                            $success_msg = "Permission added and automatically assigned to " . $user_type . "! You can add another permission or close the modal.";
                        } else {
                            $success_msg = "Permission added successfully but assignment failed. You may need to manually assign it. You can add another permission or close the modal.";
                        }

                        if ($is_ajax_request) {
                            header('Content-Type: application/json');
                            echo json_encode(['success' => true, 'message' => $success_msg]);
                            exit();
                        } else {
                            $_SESSION['success'] = "Permission added successfully.";
                            header('location: permissions.php');
                            exit();
                        }
                    } else {
                        $error_msg = "Failed to insert permission into database.";
                        if ($is_ajax_request) {
                            header('Content-Type: application/json');
                            echo json_encode(['success' => false, 'message' => $error_msg]);
                            exit();
                        } else {
                            $_SESSION['error'] = $error_msg;
                        }
                    }
                }
            }
        } catch (PDOException $e) {
            // Log the error for debugging
            error_log("Permission add error: " . $e->getMessage());
            error_log("SQL State: " . $e->getCode());
            error_log("Stack trace: " . $e->getTraceAsString());

            $error_msg = "Database error: " . $e->getMessage();
            if ($is_ajax_request) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => $error_msg, 'debug' => $e->getCode()]);
                exit();
            } else {
                $_SESSION['error'] = $error_msg;
            }
        } catch (Exception $e) {
            // Log any other errors
            error_log("General error in permission add: " . $e->getMessage());

            $error_msg = "An unexpected error occurred: " . $e->getMessage();
            if ($is_ajax_request) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => $error_msg]);
                exit();
            } else {
                $_SESSION['error'] = $error_msg;
            }
        }
    }
    
    // Update permission
    if (isset($_POST['update_permission'])) {
        $permission_id = $_POST['permission_id'];
        $user_type = $_POST['user_type'];
        $module = $_POST['module'];
        $permission_name = $_POST['permission_name'];
        $description = $_POST['description'];
        $status = $_POST['status'];

        try {
            // Find the role_id for the selected user_type
            $role_id = null;
            foreach ($roles as $role) {
                if ($role['role_name'] == $user_type) {
                    $role_id = $role['role_id'];
                    break;
                }
            }

            if (!$role_id) {
                $_SESSION['error'] = "Invalid user type selected.";
            } else {
                // Update permission with role_id
                $stmt = $conn->prepare("UPDATE permissions SET role_id = ?, module = ?, permission_name = ?, description = ?, status = ? WHERE permission_id = ?");
                $stmt->execute([$role_id, $module, $permission_name, $description, $status, $permission_id]);

                $_SESSION['success'] = "Permission updated successfully.";
                header('location: permissions.php');
                exit();
            }
        } catch (PDOException $e) {
            $_SESSION['error'] = "Error updating permission: " . $e->getMessage();
        }
    }
    
    // Delete permission
    if (isset($_POST['delete_permission'])) {
        $permission_id = $_POST['permission_id'];

        try {
            // Start transaction
            $conn->beginTransaction();

            // First delete from user_permissions table
            $stmt = $conn->prepare("DELETE FROM user_permissions WHERE permission_id = ?");
            $stmt->execute([$permission_id]);

            // Then delete from permissions table
            $stmt = $conn->prepare("DELETE FROM permissions WHERE permission_id = ?");
            $stmt->execute([$permission_id]);

            // Commit transaction
            $conn->commit();

            $_SESSION['success'] = "Permission deleted successfully.";
            header('location: permissions.php');
            exit();
        } catch (PDOException $e) {
            // Rollback on error
            $conn->rollback();
            $_SESSION['error'] = "Error deleting permission: " . $e->getMessage();
        }
    }
    
    // Assign role permissions
    if (isset($_POST['assign_permissions'])) {
        $role_id = $_POST['role_id'];
        $permissions = isset($_POST['permissions']) ? $_POST['permissions'] : [];
        
        try {
            // Begin transaction
            $conn->beginTransaction();
            
            // First remove all existing permissions for this role
            $stmt = $conn->prepare("DELETE FROM user_permissions WHERE role_id = ?");
            $stmt->execute([$role_id]);
            
            // Then add selected permissions
            if (!empty($permissions)) {
                $stmt = $conn->prepare("INSERT INTO user_permissions (role_id, permission_id) VALUES (?, ?)");
                foreach ($permissions as $permission_id) {
                    $stmt->execute([$role_id, $permission_id]);
                }
            }
            
            // Commit transaction
            $conn->commit();
            
            $_SESSION['success'] = "Role permissions updated successfully.";
            header('location: permissions.php');
            exit();
        } catch (PDOException $e) {
            // Rollback transaction on error
            $conn->rollBack();
            $_SESSION['error'] = "Error assigning permissions: " . $e->getMessage();
        }
    }
}

// Roles already defined at the top of the file

// Get all modules from the system
$modules = [
    'dashboard' => 'Dashboard',
    'residents' => 'Residents Management',
    'households' => 'Households Management',
    'officials' => 'Officials Management',
    'documents' => 'Document Services',
    'complaints' => 'Complaints & Blotter',
    'bail' => 'Bail Management',
    'social' => 'Social Services',
    'health' => 'Health Services',
    'finance' => 'Financial Management',
    'properties' => 'Properties & Assets',
    'projects' => 'Projects Management',
    'governance' => 'Governance',
    'disaster' => 'Disaster Management',
    'communication' => 'Communication',
    'events' => 'Events Management',
    'reports' => 'Reports & Analytics',
    'settings' => 'System Settings',
    'users' => 'User Management',
    'admin' => 'Administration'
];

// Define comprehensive permissions for each module
$module_permissions = [
    'dashboard' => [
        'view_dashboard' => 'View Dashboard',
        'view_statistics' => 'View Statistics',
        'view_recent_activities' => 'View Recent Activities'
    ],
    'residents' => [
        'view_residents' => 'View Residents List',
        'add_resident' => 'Add New Resident',
        'edit_resident' => 'Edit Resident Information',
        'delete_resident' => 'Delete Resident',
        'view_resident_details' => 'View Resident Details',
        'manage_migration' => 'Manage Migration Records',
        'add_migration' => 'Add Migration Records',
        'edit_migration' => 'Edit Migration Records',
        'delete_migration' => 'Delete Migration Records',
        'view_migration' => 'View Migration Records'
    ],
    'households' => [
        'view_households' => 'View Households',
        'add_household' => 'Add New Household',
        'edit_household' => 'Edit Household Information',
        'delete_household' => 'Delete Household',
        'manage_family_relationships' => 'Manage Family Relationships',
        'add_family_relationship' => 'Add Family Relationships',
        'edit_family_relationship' => 'Edit Family Relationships',
        'delete_family_relationship' => 'Delete Family Relationships',
        'view_family_relationship' => 'View Family Relationships'
    ],
    'officials' => [
        'view_officials' => 'View Officials',
        'add_official' => 'Add New Official',
        'edit_official' => 'Edit Official Information',
        'delete_official' => 'Delete Official',
        'manage_staff' => 'Manage Staff Members'
    ],
    'documents' => [
        'view_documents' => 'View Document Requests',
        'edit_document' => 'Edit Document Requests',
        'delete_document' => 'Delete Document Requests',
        'issue_clearance' => 'Issue Barangay Clearance',
        'issue_certificate' => 'Issue Certificates',
        'issue_residency' => 'Issue Certificate of Residency',
        'issue_indigency' => 'Issue Certificate of Indigency',
        'issue_business_permit' => 'Issue Business Permit',
        'issue_good_standing' => 'Issue Certificate of Good Standing',
        'issue_no_pending_case' => 'Issue Certificate of No Pending Case',
        'issue_demolition' => 'Issue Certificate of Demolition',
        'issue_solo_parents' => 'Issue Solo Parents Certificate',
        'issue_toda_certificate' => 'Issue TODA Certificate',
        'manage_templates' => 'Manage Document Templates',
        'print_documents' => 'Print Documents'
    ],
    'complaints' => [
        'view_complaints' => 'View Complaints',
        'add_complaint' => 'File New Complaint',
        'edit_complaint' => 'Edit Complaint',
        'delete_complaint' => 'Delete Complaint',
        'resolve_complaint' => 'Resolve Complaint',
        'view_hearings' => 'View Hearings',
        'schedule_hearing' => 'Schedule Hearing',
        'update_hearing' => 'Update/Edit Hearing',
        'delete_hearing' => 'Delete Hearing',
        'manage_hearings' => 'Manage Hearings',
        'view_resolutions' => 'View Resolutions',
        'add_resolution' => 'Add Resolution'
    ],
    'bail' => [
        'view_bail' => 'View Bail Records',
        'add_bail' => 'Add Bail Record',
        'edit_bail' => 'Edit Bail Record',
        'delete_bail' => 'Delete Bail Record',
        'manage_bail_status' => 'Manage Bail Status',
        'process_bail_payment' => 'Process Bail Payment',
        'generate_bail_receipt' => 'Generate Bail Receipt'
    ],
    'social' => [
        'view_social_services' => 'View Social Services',
        'manage_seniors' => 'Manage Senior Citizens',
        'manage_pwd' => 'Manage PWD Records',
        'manage_medical_assistance' => 'Manage Medical Assistance',
        'manage_financial_assistance' => 'Manage Financial Assistance',
        'manage_scholarships' => 'Manage Scholarship Programs',
        'manage_relief_programs' => 'Manage Relief Programs'
    ],
    'health' => [
        'view_health_records' => 'View Health Records',
        'manage_health_records' => 'Manage Health Records',
        'manage_disease_monitoring' => 'Manage Disease Monitoring',
        'manage_immunization' => 'Manage Immunization Records'
    ],
    'finance' => [
        'view_budget' => 'View Budget',
        'manage_budget' => 'Manage Budget',
        'view_expenses' => 'View Expenses',
        'add_expense' => 'Add Expense',
        'edit_expense' => 'Edit Expense',
        'delete_expense' => 'Delete Expense',
        'view_revenue' => 'View Revenue',
        'manage_revenue' => 'Manage Revenue'
    ],
    'properties' => [
        'view_properties' => 'View Properties',
        'add_property' => 'Add Property',
        'edit_property' => 'Edit Property',
        'delete_property' => 'Delete Property',
        'manage_maintenance' => 'Manage Maintenance Records'
    ],
    'projects' => [
        'view_projects' => 'View Projects',
        'add_project' => 'Add Project',
        'edit_project' => 'Edit Project',
        'delete_project' => 'Delete Project',
        'manage_project_status' => 'Manage Project Status'
    ],
    'governance' => [
        'view_ordinances' => 'View Ordinances',
        'add_ordinance' => 'Add Ordinance',
        'edit_ordinance' => 'Edit Ordinance',
        'delete_ordinance' => 'Delete Ordinance',
        'view_executive_orders' => 'View Executive Orders',
        'add_executive_order' => 'Add Executive Order',
        'edit_executive_order' => 'Edit Executive Order',
        'delete_executive_order' => 'Delete Executive Order'
    ],
    'disaster' => [
        'view_disaster_management' => 'View Disaster Management',
        'add_disaster_record' => 'Add Disaster Record',
        'edit_disaster_record' => 'Edit Disaster Record',
        'delete_disaster_record' => 'Delete Disaster Record',
        'manage_preparedness' => 'Manage Disaster Preparedness'
    ],
    'events' => [
        'view_events' => 'View Events',
        'add_event' => 'Add Event',
        'edit_event' => 'Edit Event',
        'delete_event' => 'Delete Event',
        'manage_event_registration' => 'Manage Event Registration'
    ],
    'communication' => [
        'view_announcements' => 'View Announcements',
        'add_announcement' => 'Add Announcement',
        'edit_announcement' => 'Edit Announcement',
        'delete_announcement' => 'Delete Announcement',
        'manage_notifications' => 'Manage Notifications',
        'send_sms' => 'Send SMS Messages',
        'send_email' => 'Send Email Messages'
    ],
    'reports' => [
        'view_reports' => 'View Reports',
        'generate_reports' => 'Generate Reports',
        'export_reports' => 'Export Reports',
        'view_analytics' => 'View Analytics'
    ],
    'settings' => [
        'view_settings' => 'View System Settings',
        'manage_general_settings' => 'Manage General Settings',
        'manage_backup_settings' => 'Manage Backup Settings',
        'manage_email_settings' => 'Manage Email Settings',
        'manage_templates' => 'Manage Templates'
    ],
    'users' => [
        'view_users' => 'View Users',
        'add_user' => 'Add User',
        'edit_user' => 'Edit User',
        'delete_user' => 'Delete User',
        'manage_permissions' => 'Manage User Permissions',
        'view_user_logs' => 'View User Activity Logs'
    ],
    'admin' => [
        'view_logs' => 'View System Logs',
        'manage_database' => 'Manage Database',
        'system_maintenance' => 'System Maintenance',
        'backup_restore' => 'Backup & Restore'
    ]
];

// Get all permissions with role info
try {
    // Check if permissions table exists, if not create it
    $checkTable = $conn->prepare("SHOW TABLES LIKE 'permissions'");
    $checkTable->execute();
    
    if ($checkTable->rowCount() == 0) {
        // Create permissions table with correct schema
        $conn->exec("CREATE TABLE permissions (
            permission_id INT AUTO_INCREMENT PRIMARY KEY,
            role_id INT NOT NULL,
            module VARCHAR(50) NOT NULL,
            permission_name VARCHAR(100) NOT NULL,
            description TEXT,
            status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_permission (role_id, module, permission_name)
        )");
        
        // Add basic permissions for non-Admin user types only
        // Admin has all permissions by default and doesn't need database entries
        $basic_user_types = ['Secretary', 'Chairman', 'Kagawad', 'Staff'];
        $stmt = $conn->prepare("INSERT INTO permissions (role_id, module, permission_name, description, status) VALUES (?, ?, ?, ?, ?)");

        // Basic permissions for each user type with role_id mapping
        $basic_permissions = [
            1 => ['dashboard' => ['view_dashboard'], 'residents' => ['view_residents', 'add_resident'], 'documents' => ['view_documents', 'issue_clearance']], // Secretary
            2 => ['dashboard' => ['view_dashboard'], 'residents' => ['view_residents'], 'governance' => ['view_ordinances']], // Chairman
            3 => ['dashboard' => ['view_dashboard'], 'residents' => ['view_residents'], 'reports' => ['view_reports']], // Kagawad
            4 => ['dashboard' => ['view_dashboard'], 'residents' => ['view_residents'], 'documents' => ['view_documents']] // Staff
        ];

        foreach ($basic_permissions as $role_id => $modules) {
            foreach ($modules as $module => $permissions) {
                foreach ($permissions as $permission_name) {
                    $description = ucwords(str_replace('_', ' ', $permission_name));
                    try {
                        $stmt->execute([$role_id, $module, $permission_name, $description, 'active']);
                    } catch (PDOException $e) {
                        // Skip if permission already exists
                        if ($e->getCode() != 23000) {
                            error_log("Error adding permission: " . $e->getMessage());
                        }
                    }
                }
            }
        }

        // Log the creation
        error_log('Created permissions table with default permissions');
    }

    // Auto-population disabled - user wants to create their own permissions from scratch
    // No automatic permission creation
    
    // Check if user_permissions table exists, if not create it
    $checkTable = $conn->prepare("SHOW TABLES LIKE 'user_permissions'");
    $checkTable->execute();
    
    if ($checkTable->rowCount() == 0) {
        // Create user_permissions table
        $conn->exec("CREATE TABLE user_permissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            role_id INT NOT NULL,
            permission_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_role_permission (role_id, permission_id),
            FOREIGN KEY (permission_id) REFERENCES permissions(permission_id) ON DELETE CASCADE
        )");
        
        // Give admin role all default permissions
        $stmt = $conn->prepare("SELECT permission_id FROM permissions");
        $stmt->execute();
        $defaultPermIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $stmt = $conn->prepare("INSERT INTO user_permissions (role_id, permission_id) VALUES (1, ?)");
        foreach ($defaultPermIds as $permId) {
            $stmt->execute([$permId]);
        }
        
        // Log the creation
        error_log('Created user_permissions table with default assignments');
    }
    
    // Now query the permissions - since the database uses role_id, we need to get all permissions
    $stmt = $conn->query("
        SELECT p.*, 'All Roles' as role_name
        FROM permissions p
        ORDER BY p.module, p.permission_name
    ");
    $permissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $permissions = [];
    $_SESSION['error'] = "Error fetching permissions: " . $e->getMessage();
}

// Get current role for permission assignment
$selected_user_type = isset($_GET['user_type']) ? $_GET['user_type'] : (isset($roles[0]['role_name']) ? $roles[0]['role_name'] : '');
$selected_role_name = $selected_user_type;
$from_users = isset($_GET['from']) && $_GET['from'] === 'users';

// Get assigned permissions for selected user type
$assigned_permissions = [];
if (!empty($selected_user_type)) {
    try {
        // First, get the role_id for the selected user type
        $role_id = null;
        foreach ($roles as $role) {
            if ($role['role_name'] == $selected_user_type) {
                $role_id = $role['role_id'];
                break;
            }
        }

        if ($role_id) {
            // Get assigned permissions using role_id and user_permissions table
            $stmt = $conn->prepare("
                SELECT p.permission_id, p.permission_name
                FROM permissions p
                INNER JOIN user_permissions up ON p.permission_id = up.permission_id
                WHERE up.role_id = ? AND p.status = 'active'
            ");
            $stmt->execute([$role_id]);
            $assigned = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Convert to associative array for faster lookups using permission_id as key
            foreach ($assigned as $perm) {
                $assigned_permissions[$perm['permission_id']] = true;
            }
        }
    } catch (PDOException $e) {
        $_SESSION['error'] = "Error fetching assigned permissions: " . $e->getMessage();
    }
}

// If this is an AJAX request, don't output HTML
if ($is_ajax_request) {
    // AJAX requests should have been handled above and exited
    // If we reach here, something went wrong
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'AJAX request not properly handled']);
    exit();
}

// Set page title
$page_title = "User Permissions - Admin Panel";

// Include header and navbar
include '../includes/header.php';
include '../includes/sidebar.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar included above -->
        
        <!-- Main Content -->
        <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
            <!-- Content Header -->
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">🔐 User Permissions Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <?php if ($from_users): ?>
                    <div class="btn-group me-2">
                        <a href="users.php" class="btn btn-outline-secondary">
                            ← Back to Users
                        </a>
                    </div>
                    <?php endif; ?>
                    <div class="btn-group">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPermissionModal">
                            ➕ Add Permission
                        </button>
                    </div>
                </div>
            </div>

            <!-- Main content -->
            <section class="content">
                <div class="container-fluid">
                    <?php if (isset($_SESSION['success'])): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php 
                            echo $_SESSION['success'];
                            unset($_SESSION['success']);
                            ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($_SESSION['error'])): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php 
                            echo $_SESSION['error'];
                            unset($_SESSION['error']);
                            ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <div class="row">
                        <!-- Assign Permissions Card -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Assign Permissions to Role</h3>
                                </div>
                                <div class="card-body">
                                    <form method="get" class="mb-3">
                                        <div class="form-group">
                                            <label for="user_type">Select User Type:</label>
                                            <select name="user_type" id="user_type" class="form-control" onchange="this.form.submit()">
                                                <?php foreach ($roles as $role): ?>
                                                    <option value="<?php echo $role['role_name']; ?>" <?php echo $selected_user_type == $role['role_name'] ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($role['role_name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <?php if ($from_users): ?>
                                        <input type="hidden" name="from" value="users">
                                        <?php endif; ?>
                                    </form>
                                    
                                    <?php if (!empty($selected_user_type)): ?>
                                        <?php
                                        // Get role_id for selected user type
                                        $selected_role_id = null;
                                        foreach ($roles as $role) {
                                            if ($role['role_name'] == $selected_user_type) {
                                                $selected_role_id = $role['role_id'];
                                                break;
                                            }
                                        }
                                        ?>
                                        <form method="post" action="">
                                            <input type="hidden" name="role_id" value="<?php echo $selected_role_id; ?>">
                                            
                                            <h5>Permissions for: <?php echo htmlspecialchars($selected_role_name); ?></h5>

                                            <!-- Check All / Uncheck All buttons -->
                                            <div class="mb-3">
                                                <button type="button" class="btn btn-sm btn-success me-2" onclick="checkAllPermissions()">
                                                    <i class="fas fa-check-square"></i> Check All
                                                </button>
                                                <button type="button" class="btn btn-sm btn-secondary" onclick="uncheckAllPermissions()">
                                                    <i class="fas fa-square"></i> Uncheck All
                                                </button>
                                            </div>

                                            <div class="table-responsive">
                                                <table class="table table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>Module</th>
                                                            <th>Permission</th>
                                                            <th>Description</th>
                                                            <th>Assign</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach ($permissions as $permission): ?>
                                                            <tr>
                                                                <td><?php echo htmlspecialchars($permission['module']); ?></td>
                                                                <td><?php echo htmlspecialchars($permission['permission_name']); ?></td>
                                                                <td><?php echo htmlspecialchars($permission['description']); ?></td>
                                                                <td>
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="checkbox" 
                                                                               name="permissions[]" value="<?php echo $permission['permission_id']; ?>"
                                                                               <?php echo isset($assigned_permissions[$permission['permission_id']]) ? 'checked' : ''; ?>>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        <?php endforeach; ?>
                                                        <?php if (empty($permissions)): ?>
                                                            <tr>
                                                                <td colspan="4" class="text-center">No permissions found.</td>
                                                            </tr>
                                                        <?php endif; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                            
                                            <button type="submit" name="assign_permissions" class="btn btn-primary">
                                                <i class="fas fa-save"></i> Save Permissions
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Manage Permissions Card -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Manage Permissions</h3>
                                    <div class="card-tools">
                                        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addPermissionModal">
                                            <i class="fas fa-plus"></i> Add Permission
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Role</th>
                                                    <th>Module</th>
                                                    <th>Permission</th>
                                                    <th>Status</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($permissions as $permission): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($permission['role_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($permission['module']); ?></td>
                                                        <td><?php echo htmlspecialchars($permission['permission_name']); ?></td>
                                                        <td>
                                                            <span class="badge <?php echo $permission['status'] == 'active' ? 'bg-success' : 'bg-danger'; ?>">
                                                                <?php echo ucfirst($permission['status']); ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#editPermissionModal" 
                                                                    data-id="<?php echo $permission['permission_id']; ?>"
                                                                    data-role="<?php echo $permission['role_id']; ?>"
                                                                    data-module="<?php echo $permission['module']; ?>"
                                                                    data-name="<?php echo $permission['permission_name']; ?>"
                                                                    data-desc="<?php echo $permission['description']; ?>"
                                                                    data-status="<?php echo $permission['status']; ?>">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deletePermissionModal"
                                                                    data-id="<?php echo $permission['permission_id']; ?>"
                                                                    data-name="<?php echo $permission['permission_name']; ?>">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                                <?php if (empty($permissions)): ?>
                                                    <tr>
                                                        <td colspan="5" class="text-center">No permissions found.</td>
                                                    </tr>
                                                <?php endif; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>
</div>

<!-- Add Permission Modal -->
<div class="modal fade" id="addPermissionModal" tabindex="-1" aria-labelledby="addPermissionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addPermissionModalLabel">Add New Permission</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addPermissionForm" method="post" action="">
                <div class="modal-body">
                    <div id="add-permission-alert" class="alert" style="display: none;"></div>
                    <div class="form-group">
                        <label for="add_user_type">User Type:</label>
                        <select name="user_type" id="add_user_type" class="form-control" required>
                            <?php foreach ($roles as $role): ?>
                                <option value="<?php echo $role['role_name']; ?>">
                                    <?php echo htmlspecialchars($role['role_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="add_module">Module:</label>
                        <select name="module" id="add_module" class="form-control" required onchange="updatePermissionOptions('add')">
                            <option value="">Select Module</option>
                            <?php foreach ($modules as $key => $value): ?>
                                <option value="<?php echo $key; ?>">
                                    <?php echo htmlspecialchars($value); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="add_permission_name">Permission Name:</label>
                        <select name="permission_name" id="add_permission_name" class="form-control" required onchange="updateDescription('add')" disabled>
                            <option value="">Select Module First</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="add_description">Description:</label>
                        <textarea name="description" id="add_description" class="form-control" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" name="add_permission" class="btn btn-primary" id="addPermissionBtn">Add Permission</button>
                    <button type="button" class="btn btn-success" id="addAnotherBtn" style="display: none;">Add Another</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Permission Modal -->
<div class="modal fade" id="editPermissionModal" tabindex="-1" aria-labelledby="editPermissionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editPermissionModalLabel">Edit Permission</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <input type="hidden" name="permission_id" id="edit_permission_id">
                    <div class="form-group">
                        <label for="edit_user_type">User Type:</label>
                        <select name="user_type" id="edit_user_type" class="form-control" required>
                            <?php foreach ($roles as $role): ?>
                                <option value="<?php echo $role['role_name']; ?>">
                                    <?php echo htmlspecialchars($role['role_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="edit_module">Module:</label>
                        <select name="module" id="edit_module" class="form-control" required onchange="updatePermissionOptions('edit')">
                            <?php foreach ($modules as $key => $value): ?>
                                <option value="<?php echo $key; ?>">
                                    <?php echo htmlspecialchars($value); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="edit_permission_name">Permission Name:</label>
                        <select name="permission_name" id="edit_permission_name" class="form-control" required onchange="updateDescription('edit')" disabled>
                            <option value="">Select Module First</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="edit_description">Description:</label>
                        <textarea name="description" id="edit_description" class="form-control" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="edit_status">Status:</label>
                        <select name="status" id="edit_status" class="form-control" required>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" name="update_permission" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Permission Modal -->
<div class="modal fade" id="deletePermissionModal" tabindex="-1" aria-labelledby="deletePermissionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deletePermissionModalLabel">Delete Permission</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <input type="hidden" name="permission_id" id="delete_permission_id">
                    <p>Are you sure you want to delete the permission: <span id="delete_permission_name"></span>?</p>
                    <p class="text-danger">This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="delete_permission" class="btn btn-danger">Delete</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>

<script>
// Module permissions mapping
var modulePermissions = <?php echo json_encode($module_permissions); ?>;

// Function to update permission options based on selected module
function updatePermissionOptions(modalType) {
    var moduleSelect = document.getElementById(modalType + '_module');
    var permissionSelect = document.getElementById(modalType + '_permission_name');
    var selectedModule = moduleSelect.value;

    // Clear existing options
    permissionSelect.innerHTML = '';

    if (selectedModule && modulePermissions[selectedModule]) {
        // Add default option
        var defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = 'Select Permission';
        permissionSelect.appendChild(defaultOption);

        // Add permissions for selected module
        var permissions = modulePermissions[selectedModule];
        for (var permissionKey in permissions) {
            var option = document.createElement('option');
            option.value = permissionKey;
            option.textContent = permissions[permissionKey];
            permissionSelect.appendChild(option);
        }

        // Enable the permission select
        permissionSelect.disabled = false;
    } else {
        // Add default option when no module selected
        var defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = 'Select Module First';
        permissionSelect.appendChild(defaultOption);

        // Disable the permission select
        permissionSelect.disabled = true;
    }
}

// Function to update description based on selected permission
function updateDescription(modalType) {
    var moduleSelect = document.getElementById(modalType + '_module');
    var permissionSelect = document.getElementById(modalType + '_permission_name');
    var descriptionTextarea = document.getElementById(modalType + '_description');

    var selectedModule = moduleSelect.value;
    var selectedPermission = permissionSelect.value;

    if (selectedModule && selectedPermission && modulePermissions[selectedModule] && modulePermissions[selectedModule][selectedPermission]) {
        descriptionTextarea.value = modulePermissions[selectedModule][selectedPermission];
    }
}

// Edit permission modal
$('#editPermissionModal').on('show.bs.modal', function (event) {
    var button = $(event.relatedTarget);
    var id = button.data('id');
    var roleId = button.data('role');
    var module = button.data('module');
    var name = button.data('name');
    var description = button.data('desc');
    var status = button.data('status');

    var modal = $(this);
    modal.find('#edit_permission_id').val(id);
    modal.find('#edit_role_id').val(roleId);
    modal.find('#edit_module').val(module);

    // Update permission options for the selected module
    updatePermissionOptions('edit');

    // Set the permission name after options are populated
    setTimeout(function() {
        modal.find('#edit_permission_name').val(name);
    }, 100);

    modal.find('#edit_description').val(description);
    modal.find('#edit_status').val(status);
});

// Delete permission modal
$('#deletePermissionModal').on('show.bs.modal', function (event) {
    var button = $(event.relatedTarget);
    var id = button.data('id');
    var name = button.data('name');

    var modal = $(this);
    modal.find('#delete_permission_id').val(id);
    modal.find('#delete_permission_name').text(name);
});

// Add permission modal initialization
$('#addPermissionModal').on('show.bs.modal', function (event) {
    // Reset form
    this.querySelector('form').reset();

    // Reset permission dropdown
    var permissionSelect = document.getElementById('add_permission_name');
    permissionSelect.innerHTML = '<option value="">Select Module First</option>';
    permissionSelect.disabled = true;

    // Clear description
    document.getElementById('add_description').value = '';
});

// Initialize permission dropdowns when page loads
$(document).ready(function() {
    // Initialize add modal permission dropdown
    var addPermissionSelect = document.getElementById('add_permission_name');
    if (addPermissionSelect) {
        addPermissionSelect.disabled = true;
    }

    // Initialize edit modal permission dropdown
    var editPermissionSelect = document.getElementById('edit_permission_name');
    if (editPermissionSelect) {
        editPermissionSelect.disabled = true;
    }


});

// AJAX form submission for adding permissions
$('#addPermissionForm').on('submit', function(e) {
    e.preventDefault();

    var formData = $(this).serialize() + '&ajax=1&add_permission=1';
    var submitBtn = $('#addPermissionBtn');
    var addAnotherBtn = $('#addAnotherBtn');
    var alertDiv = $('#add-permission-alert');

    // Validate form data before sending
    var userType = $('#add_user_type').val();
    var module = $('#add_module').val();
    var permissionName = $('#add_permission_name').val();

    if (!userType || !module || !permissionName) {
        alertDiv.removeClass('alert-success').addClass('alert-danger')
            .html('<i class="fas fa-exclamation-triangle"></i> Please fill in all required fields.')
            .show();
        return;
    }

    console.log('Submitting form data:', formData);

    // Disable submit button and show loading
    submitBtn.prop('disabled', true).text('Adding...');

    $.ajax({
        url: 'permissions.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(result) {
            console.log('Server response:', result);

            if (result.success) {
                // Show success message
                alertDiv.removeClass('alert-danger').addClass('alert-success')
                    .html('<i class="fas fa-check-circle"></i> ' + result.message)
                    .show();

                // Reset form for next entry
                resetAddForm();

                // Show "Add Another" button and hide submit button
                submitBtn.hide();
                addAnotherBtn.show();

                // Refresh the permissions table without closing modal
                setTimeout(function() {
                    // Reload only the permissions table content
                    refreshPermissionsTable();
                }, 1000);

            } else {
                // Show error message
                alertDiv.removeClass('alert-success').addClass('alert-danger')
                    .html('<i class="fas fa-exclamation-triangle"></i> ' + result.message)
                    .show();

                // Re-enable submit button
                submitBtn.prop('disabled', false).text('Add Permission');
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', status, error);
            console.log('Response Text:', xhr.responseText);
            console.log('Status Code:', xhr.status);

            var errorMessage = 'Network error occurred';
            if (xhr.responseText) {
                try {
                    var errorResponse = JSON.parse(xhr.responseText);
                    errorMessage = errorResponse.message || errorMessage;
                } catch (e) {
                    errorMessage = 'Server returned invalid response: ' + xhr.responseText.substring(0, 100);
                }
            }

            alertDiv.removeClass('alert-success').addClass('alert-danger')
                .html('<i class="fas fa-exclamation-triangle"></i> ' + errorMessage)
                .show();

            submitBtn.prop('disabled', false).text('Add Permission');
        }
    });
});

// Add Another button functionality
$('#addAnotherBtn').on('click', function() {
    var alertDiv = $('#add-permission-alert');
    var submitBtn = $('#addPermissionBtn');
    var addAnotherBtn = $('#addAnotherBtn');

    // Hide alert and reset buttons
    alertDiv.hide();
    submitBtn.show().prop('disabled', false).text('Add Permission');
    addAnotherBtn.hide();

    // Reset form
    resetAddForm();
});

// Function to reset the add form
function resetAddForm() {
    // Reset form fields but keep user type selected
    var currentUserType = $('#add_user_type').val();
    $('#add_module').val('');
    $('#add_permission_name').html('<option value="">Select Module First</option>').prop('disabled', true);
    $('#add_description').val('');

    // Keep the same user type selected for convenience
    $('#add_user_type').val(currentUserType);
}

// Function to check all permission checkboxes
function checkAllPermissions() {
    const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
}

// Function to uncheck all permission checkboxes
function uncheckAllPermissions() {
    const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
}

// Function to refresh permissions table without closing modal
function refreshPermissionsTable() {
    // Reload the permissions table content via AJAX
    $.ajax({
        url: 'permissions.php',
        type: 'GET',
        data: {
            ajax_refresh: 1,
            user_type: $('#add_user_type').val() || '<?php echo isset($_GET["user_type"]) ? $_GET["user_type"] : ""; ?>'
        },
        success: function(response) {
            // Find and update the permissions table
            var newContent = $(response).find('.card-body .table-responsive');
            if (newContent.length > 0) {
                $('.card-body .table-responsive').html(newContent.html());
            }

            // Update the permissions count if it exists
            var newCount = $(response).find('.badge.bg-primary').text();
            if (newCount) {
                $('.badge.bg-primary').text(newCount);
            }
        },
        error: function() {
            console.log('Failed to refresh permissions table');
        }
    });
}
</script>
</body>
</html>