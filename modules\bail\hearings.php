<?php
// Include database connection
include '../../includes/session.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include permission functions
include '../../includes/functions/permission_functions.php';

// Check if user has permission to access bail module
if (!canAccessModule('bail')) {
    $_SESSION['error'] = 'You do not have permission to access this page.';
    header('Location: ../../index.php');
    exit();
}

// Set page title
$page_title = 'Court Hearings';

// Get filter parameters
$status_filter = isset($_GET['status']) ? $_GET['status'] : 'upcoming';
$blotter_id = isset($_GET['blotter_id']) ? $_GET['blotter_id'] : null;

// Build WHERE clause for filtering
$where_clauses = [];
$params = [];

if ($blotter_id) {
    $where_clauses[] = "ch.blotter_id = :blotter_id";
    $params[':blotter_id'] = $blotter_id;
}

if ($status_filter == 'upcoming') {
    $where_clauses[] = "ch.hearing_date >= NOW()";
} elseif ($status_filter == 'past') {
    $where_clauses[] = "ch.hearing_date < NOW()";
}

$where_sql = !empty($where_clauses) ? 'WHERE ' . implode(' AND ', $where_clauses) : '';

// Fetch hearings with pagination
$current_page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$records_per_page = 10;
$offset = ($current_page - 1) * $records_per_page;

try {
    // Count total records for pagination
    $count_sql = "SELECT COUNT(*) AS total FROM court_hearings ch $where_sql";
    $count_stmt = $conn->prepare($count_sql);
    foreach ($params as $param_key => $param_value) {
        $count_stmt->bindValue($param_key, $param_value);
    }
    $count_stmt->execute();
    $total_records = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
    $total_pages = ceil($total_records / $records_per_page);

    // Get hearings
    $sql = "
        SELECT ch.*, be.reference_number, be.suspect_name, be.case_type,
               u.username as recorded_by_name
        FROM court_hearings ch
        JOIN blotter_entries be ON ch.blotter_id = be.blotter_id
        LEFT JOIN users u ON ch.recorded_by = u.user_id
        $where_sql
        ORDER BY ch.hearing_date " . ($status_filter == 'upcoming' ? 'ASC' : 'DESC') . "
        LIMIT :offset, :limit
    ";
    
    $stmt = $conn->prepare($sql);
    foreach ($params as $param_key => $param_value) {
        $stmt->bindValue($param_key, $param_value);
    }
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindValue(':limit', $records_per_page, PDO::PARAM_INT);
    $stmt->execute();
    $hearings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // If filtered by blotter_id, get case details
    if ($blotter_id) {
        $stmt = $conn->prepare("SELECT * FROM blotter_entries WHERE blotter_id = :blotter_id");
        $stmt->bindParam(':blotter_id', $blotter_id);
        $stmt->execute();
        $case = $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error: ' . $e->getMessage();
    $hearings = [];
    $total_pages = 0;
}

// Process update status action
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    try {
        $hearing_id = $_POST['hearing_id'];
        $attendance_status = $_POST['attendance_status'];
        $notes = $_POST['notes'];
        
        $sql = "UPDATE court_hearings SET 
                attendance_status = :attendance_status,
                notes = :notes,
                updated_by = :updated_by,
                updated_at = NOW()
            WHERE hearing_id = :hearing_id";
        
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':hearing_id', $hearing_id);
        $stmt->bindParam(':attendance_status', $attendance_status);
        $stmt->bindParam(':notes', $notes);
        $stmt->bindParam(':updated_by', $_SESSION['user_id']);
        
        if ($stmt->execute()) {
            $_SESSION['success'] = 'Hearing status updated successfully.';
        } else {
            $_SESSION['error'] = 'Failed to update hearing status.';
        }
        
        // Redirect to same page with same filters
        $redirect = 'hearings.php?';
        if ($blotter_id) $redirect .= 'blotter_id=' . $blotter_id . '&';
        $redirect .= 'status=' . $status_filter;
        header('Location: ' . $redirect);
        exit();
    } catch (PDOException $e) {
        $_SESSION['error'] = 'Database error: ' . $e->getMessage();
    }
}

// Include header and sidebar
include '../../includes/header.php';
include '../../includes/sidebar.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="mt-4">Court Hearings</h1>
            <ol class="breadcrumb mb-4">
                <li class="breadcrumb-item"><a href="../../index.php">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="index.php">Bail Management</a></li>
                <?php if ($blotter_id): ?>
                    <li class="breadcrumb-item"><a href="view_case.php?id=<?php echo $blotter_id; ?>">View Case</a></li>
                <?php else: ?>
                    <li class="breadcrumb-item"><a href="blotter.php">Blotter Entries</a></li>
                <?php endif; ?>
                <li class="breadcrumb-item active">📅 Court Hearings</li>
            </ol>

            <!-- Case Information (if filtered by case) -->
            <?php if ($blotter_id && isset($case)): ?>
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            📋 Case Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-group">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <strong>Reference Number:</strong>
                                        <span>🔢 <?php echo htmlspecialchars($case['reference_number']); ?></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <strong>Suspect Name:</strong>
                                        <span>👤 <?php echo htmlspecialchars($case['suspect_name']); ?></span>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-group">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <strong>Case Type:</strong>
                                        <span>📂 <?php echo htmlspecialchars($case['case_type']); ?></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <strong>Status:</strong>
                                        <span>
                                            <?php 
                                            $status_class = '';
                                            switch ($case['status']) {
                                                case 'Active':
                                                    $status_class = 'bg-success';
                                                    break;
                                                case 'On Bail':
                                                    $status_class = 'bg-success';
                                                    break;
                                                case 'Closed':
                                                    $status_class = 'bg-secondary';
                                                    break;
                                                case 'Transferred':
                                                    $status_class = 'bg-info';
                                                    break;
                                                case 'Dismissed':
                                                    $status_class = 'bg-danger';
                                                    break;
                                                default:
                                                    $status_class = 'bg-secondary';
                                            }
                                            ?>
                                            <span class="badge <?php echo $status_class; ?>"><?php echo htmlspecialchars($case['status']); ?></span>
                                        </span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="view_case.php?id=<?php echo $blotter_id; ?>" class="btn btn-info btn-sm">
                                👁️ View Full Case Details
                            </a>
                            <a href="schedule_hearing.php?id=<?php echo $blotter_id; ?>" class="btn btn-success btn-sm">
                                📆 Schedule New Hearing
                            </a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Filter and Action Buttons -->
            <div class="mb-4 d-flex justify-content-between align-items-center">
                <div>
                    <a href="hearings.php?status=upcoming<?php echo $blotter_id ? '&blotter_id='.$blotter_id : ''; ?>" class="btn <?php echo $status_filter == 'upcoming' ? 'btn-primary' : 'btn-outline-primary'; ?> me-2">
                        📅 Upcoming Hearings
                    </a>
                    <a href="hearings.php?status=past<?php echo $blotter_id ? '&blotter_id='.$blotter_id : ''; ?>" class="btn <?php echo $status_filter == 'past' ? 'btn-secondary' : 'btn-outline-secondary'; ?> me-2">
                        🕒 Past Hearings
                    </a>
                    
                    <?php if ($blotter_id): ?>
                        <a href="hearings.php?status=<?php echo $status_filter; ?>" class="btn btn-outline-danger">
                            🚫 Clear Case Filter
                        </a>
                    <?php endif; ?>
                </div>
                
                <div>
                    <?php if ($blotter_id): ?>
                        <a href="schedule_hearing.php?id=<?php echo $blotter_id; ?>" class="btn btn-success">
                            📆 Schedule New Hearing
                        </a>
                    <?php else: ?>
                        <a href="blotter.php" class="btn btn-primary">
                            📋 View All Cases
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Hearings List -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        📅 <?php echo $status_filter == 'upcoming' ? 'Upcoming' : 'Past'; ?> Court Hearings
                    </div>
                    <?php if ($blotter_id): ?>
                        <a href="schedule_hearing.php?id=<?php echo $blotter_id; ?>" class="btn btn-sm btn-success">
                            ➕ Add Hearing
                        </a>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <?php if (empty($hearings)): ?>
                        <div class="alert alert-info text-center" role="alert">
                            ℹ️ No <?php echo $status_filter == 'upcoming' ? 'upcoming' : 'past'; ?> hearings found.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Date & Time 📅</th>
                                        <?php if (!$blotter_id): ?>
                                            <th>Case Ref # 🔢</th>
                                            <th>Suspect 👤</th>
                                        <?php endif; ?>
                                        <th>Type 🏛️</th>
                                        <th>Location 📍</th>
                                        <th>Attendance ✅</th>
                                        <?php if ($status_filter == 'past'): ?>
                                            <th>Status 🚦</th>
                                        <?php endif; ?>
                                        <th>Actions ⚡</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($hearings as $hearing): ?>
                                        <tr>
                                            <td><?php echo date('M d, Y h:i A', strtotime($hearing['hearing_date'])); ?></td>
                                            <?php if (!$blotter_id): ?>
                                                <td>
                                                    <a href="view_case.php?id=<?php echo $hearing['blotter_id']; ?>" class="text-decoration-none">
                                                        <?php echo htmlspecialchars($hearing['reference_number']); ?>
                                                    </a>
                                                </td>
                                                <td><?php echo htmlspecialchars($hearing['suspect_name']); ?></td>
                                            <?php endif; ?>
                                            <td><?php echo htmlspecialchars($hearing['hearing_type']); ?></td>
                                            <td><?php echo htmlspecialchars($hearing['hearing_location']); ?></td>
                                            <td>
                                                <?php if ($hearing['attendance_required']): ?>
                                                    <span class="badge bg-danger">✅ Required</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">❌ Optional</span>
                                                <?php endif; ?>
                                            </td>
                                            <?php if ($status_filter == 'past'): ?>
                                                <td>
                                                    <?php 
                                                    $status_class = '';
                                                    switch ($hearing['attendance_status']) {
                                                        case 'Attended':
                                                            $status_class = 'bg-success';
                                                            break;
                                                        case 'Missed':
                                                            $status_class = 'bg-danger';
                                                            break;
                                                        case 'Rescheduled':
                                                            $status_class = 'bg-warning';
                                                            break;
                                                        case 'Canceled':
                                                            $status_class = 'bg-secondary';
                                                            break;
                                                        default:
                                                            $status_class = 'bg-info';
                                                            break;
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $status_class; ?>">
                                                        <?php echo $hearing['attendance_status'] ?: 'Not Recorded'; ?>
                                                    </span>
                                                </td>
                                            <?php endif; ?>
                                            <td>
                                                <div class="d-flex gap-1">
                                                    <a href="view_hearing.php?id=<?php echo $hearing['hearing_id']; ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="View Hearing Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <?php if ($status_filter == 'past' && (!$hearing['attendance_status'] || $hearing['attendance_status'] == '')): ?>
                                                        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#updateStatusModal<?php echo $hearing['hearing_id']; ?>" data-bs-toggle="tooltip" title="Record Attendance">
                                                            <i class="fas fa-check-circle"></i> ✅
                                                        </button>
                                                    <?php endif; ?>
                                                    <?php if ($status_filter == 'upcoming'): ?>
                                                        <a href="edit_hearing.php?id=<?php echo $hearing['hearing_id']; ?>" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="Edit Hearing">
                                                            <i class="fas fa-edit"></i> 
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                                
                                                <?php if ($status_filter == 'past' && (!$hearing['attendance_status'] || $hearing['attendance_status'] == '')): ?>
                                                    <!-- Update Status Modal -->
                                                    <div class="modal fade" id="updateStatusModal<?php echo $hearing['hearing_id']; ?>" tabindex="-1" aria-labelledby="updateStatusModalLabel<?php echo $hearing['hearing_id']; ?>" aria-hidden="true">
                                                        <div class="modal-dialog">
                                                            <div class="modal-content">
                                                                <form method="POST" action="hearings.php?status=<?php echo $status_filter; ?><?php echo $blotter_id ? '&blotter_id='.$blotter_id : ''; ?>">
                                                                    <input type="hidden" name="hearing_id" value="<?php echo $hearing['hearing_id']; ?>">
                                                                    <div class="modal-header">
                                                                        <h5 class="modal-title" id="updateStatusModalLabel<?php echo $hearing['hearing_id']; ?>">✅ Update Attendance Status</h5>
                                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                    </div>
                                                                    <div class="modal-body">
                                                                        <div class="mb-3">
                                                                            <label for="attendance_status<?php echo $hearing['hearing_id']; ?>" class="form-label">🚦 Attendance Status</label>
                                                                            <select class="form-select" id="attendance_status<?php echo $hearing['hearing_id']; ?>" name="attendance_status" required>
                                                                                <option value="">Select status</option>
                                                                                <option value="Attended">✅ Attended</option>
                                                                                <option value="Missed">❌ Missed</option>
                                                                                <option value="Rescheduled">🔄 Rescheduled</option>
                                                                                <option value="Canceled">⛔ Canceled</option>
                                                                            </select>
                                                                        </div>
                                                                        <div class="mb-3">
                                                                            <label for="notes<?php echo $hearing['hearing_id']; ?>" class="form-label">📝 Notes</label>
                                                                            <textarea class="form-control" id="notes<?php echo $hearing['hearing_id']; ?>" name="notes" rows="3"></textarea>
                                                                        </div>
                                                                    </div>
                                                                    <div class="modal-footer">
                                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                                        <button type="submit" name="update_status" class="btn btn-primary">💾 Update Status</button>
                                                                    </div>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if ($total_pages > 1): ?>
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center mt-4">
                                    <li class="page-item <?php echo ($current_page <= 1) ? 'disabled' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $current_page - 1; ?>&status=<?php echo $status_filter; ?><?php echo $blotter_id ? '&blotter_id='.$blotter_id : ''; ?>" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                        <li class="page-item <?php echo ($current_page == $i) ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?><?php echo $blotter_id ? '&blotter_id='.$blotter_id : ''; ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>
                                    <li class="page-item <?php echo ($current_page >= $total_pages) ? 'disabled' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $current_page + 1; ?>&status=<?php echo $status_filter; ?><?php echo $blotter_id ? '&blotter_id='.$blotter_id : ''; ?>" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../../includes/footer.php'; ?>

<!-- Toast container -->
<div class="toast-container position-fixed top-0 end-0 p-3">
    <div id="successToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex align-items-center p-3">
            <div class="me-2">✅</div>
            <div class="me-auto"><?php echo isset($_SESSION['success']) ? $_SESSION['success'] : (isset($_SESSION['error']) ? $_SESSION['error'] : ''); ?></div>
            <div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    </div>
</div>

<script>
    // Show toast notification if there's a message
    document.addEventListener('DOMContentLoaded', function() {
        <?php if(isset($_SESSION['success']) || isset($_SESSION['error'])): ?>
        // Update toast styling based on message type
        const toast = document.getElementById('successToast');
        
        <?php if(isset($_SESSION['success'])): ?>
        toast.style.backgroundColor = '#28a745'; // success green
        toast.style.color = 'white';
        <?php elseif(isset($_SESSION['error'])): ?>
        toast.style.backgroundColor = '#dc3545'; // danger red
        toast.style.color = 'white';
        <?php endif; ?>
        
        const successToast = new bootstrap.Toast(toast, {
            delay: 5000
        });
        successToast.show();

        <?php 
        // Clear session messages after displaying
        if(isset($_SESSION['success'])) unset($_SESSION['success']);
        if(isset($_SESSION['error'])) unset($_SESSION['error']);
        ?>
        <?php endif; ?>
    });
</script> 