<?php
// Include database connection
include '../../includes/session.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include permission functions
include_once '../../includes/functions/permission_functions.php';

// Check if user has permission to access bail module
if (!canAccessModule('bail')) {
    $_SESSION['error'] = 'You do not have permission to access this page.';
    header('Location: ../../index.php');
    exit();
}

// Set page title
$page_title = 'Report Bail Violation';

// Get blotter ID from query string
$blotter_id = isset($_GET['id']) ? $_GET['id'] : null;

// If no blotter_id provided, check if we should display a case selection form
if (!$blotter_id) {
    // Get cases that are eligible for bail violations (both on bail and those who have release orders)
    $stmt = $conn->query("
        SELECT be.blotter_id, be.reference_number, be.suspect_name, be.case_type, be.status
        FROM blotter_entries be
        LEFT JOIN release_orders ro ON be.blotter_id = ro.blotter_id
        WHERE be.status = 'On Bail' 
           OR ro.release_status = 'Released'
           OR (SELECT COUNT(*) FROM bail_payments WHERE blotter_id = be.blotter_id AND payment_status = 'Completed') > 0
        GROUP BY be.blotter_id
        ORDER BY be.created_at DESC
    ");
    $active_cases = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // We'll show a case selection form
    $case_selection = true;
} else {
    // Get case details
    $stmt = $conn->prepare("
        SELECT be.*, ba.bail_amount,
               (SELECT MAX(release_date) FROM release_orders WHERE blotter_id = be.blotter_id AND release_status = 'Released') as release_date,
               (SELECT COUNT(*) FROM bail_payments WHERE blotter_id = be.blotter_id AND payment_status = 'Completed') as has_payments,
               (SELECT COUNT(*) FROM release_orders WHERE blotter_id = be.blotter_id AND release_status = 'Released') as has_release
        FROM blotter_entries be
        LEFT JOIN bail_assessments ba ON be.blotter_id = ba.blotter_id
        WHERE be.blotter_id = :blotter_id
    ");
    $stmt->bindParam(':blotter_id', $blotter_id);
    $stmt->execute();
    $case = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$case) {
        $_SESSION['error'] = 'Case not found.';
        header('Location: index.php');
        exit();
    }
    
    // Check if the case is eligible for violation reporting
    $is_eligible = $case['status'] == 'On Bail' || $case['has_payments'] > 0 || $case['has_release'] > 0;
    
    if (!$is_eligible) {
        $_SESSION['error'] = 'This case is not eligible for violation reporting. The suspect must have either paid bail or have a release order.';
        header('Location: index.php');
        exit();
    }
    
    // Get existing violations
    $stmt = $conn->prepare("
        SELECT bv.*, u1.username as reported_by_name, u2.username as resolved_by_name
        FROM bail_violations bv
        LEFT JOIN users u1 ON bv.reported_by = u1.user_id
        LEFT JOIN users u2 ON bv.resolved_by = u2.user_id
        WHERE bv.blotter_id = :blotter_id
        ORDER BY bv.reported_at DESC
    ");
    $stmt->bindParam(':blotter_id', $blotter_id);
    $stmt->execute();
    $violations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $case_selection = false;
}

// Handle form submission for reporting a violation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['report_violation'])) {
    try {
        // Get form data
        $blotter_id = $_POST['blotter_id'];
        $violation_date = $_POST['violation_date'];
        $violation_type = $_POST['violation_type'];
        $violation_details = $_POST['violation_details'];
        $reported_by = $_SESSION['user_id'];
        $reported_at = date('Y-m-d H:i:s');
        $status = 'Reported';
        
        // Handle file upload for evidence
        $evidence_files = '';
        if (!empty($_FILES['evidence_files']['name'][0])) {
            $upload_dir = '../../uploads/violations/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }
            
            $file_names = [];
            foreach ($_FILES['evidence_files']['name'] as $key => $name) {
                if ($_FILES['evidence_files']['error'][$key] == 0) {
                    $tmp_name = $_FILES['evidence_files']['tmp_name'][$key];
                    $ext = pathinfo($name, PATHINFO_EXTENSION);
                    $new_name = 'evidence_' . $blotter_id . '_' . date('YmdHis') . '_' . $key . '.' . $ext;
                    
                    if (move_uploaded_file($tmp_name, $upload_dir . $new_name)) {
                        $file_names[] = $new_name;
                    }
                }
            }
            
            $evidence_files = !empty($file_names) ? implode(',', $file_names) : '';
        }
        
        // Insert violation record
        $sql = "INSERT INTO bail_violations (
                blotter_id, violation_date, violation_type, violation_details,
                reported_by, reported_at, evidence_files, status
            ) VALUES (
                :blotter_id, :violation_date, :violation_type, :violation_details,
                :reported_by, :reported_at, :evidence_files, :status
            )";
            
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':blotter_id', $blotter_id);
        $stmt->bindParam(':violation_date', $violation_date);
        $stmt->bindParam(':violation_type', $violation_type);
        $stmt->bindParam(':violation_details', $violation_details);
        $stmt->bindParam(':reported_by', $reported_by);
        $stmt->bindParam(':reported_at', $reported_at);
        $stmt->bindParam(':evidence_files', $evidence_files);
        $stmt->bindParam(':status', $status);
        
        if ($stmt->execute()) {
            $_SESSION['success'] = 'Bail violation has been successfully reported.';
            header('Location: view_case.php?id=' . $blotter_id);
            exit();
        } else {
            $_SESSION['error'] = 'Failed to report bail violation.';
        }
        
    } catch (PDOException $e) {
        $_SESSION['error'] = 'Database error: ' . $e->getMessage();
    }
}

// Handle form submission for resolving a violation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['resolve_violation'])) {
    try {
        // Get form data
        $violation_id = $_POST['violation_id'];
        $blotter_id = $_POST['blotter_id'];
        $status = $_POST['resolution_status']; // 'Resolved' or 'Dismissed'
        $resolution = $_POST['resolution'];
        $action_taken = $_POST['action_taken'];
        $resolved_by = $_SESSION['user_id'];
        $resolved_at = date('Y-m-d H:i:s');
        
        // Update violation record
        $sql = "UPDATE bail_violations SET 
                status = :status, 
                resolution = :resolution, 
                action_taken = :action_taken,
                resolved_by = :resolved_by,
                resolved_at = :resolved_at
                WHERE violation_id = :violation_id";
            
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':resolution', $resolution);
        $stmt->bindParam(':action_taken', $action_taken);
        $stmt->bindParam(':resolved_by', $resolved_by);
        $stmt->bindParam(':resolved_at', $resolved_at);
        $stmt->bindParam(':violation_id', $violation_id);
        
        if ($stmt->execute()) {
            $_SESSION['success'] = 'Bail violation has been successfully ' . strtolower($status) . '.';
            header('Location: report_violation.php?id=' . $blotter_id);
            exit();
        } else {
            $_SESSION['error'] = 'Failed to update violation status.';
        }
        
    } catch (PDOException $e) {
        $_SESSION['error'] = 'Database error: ' . $e->getMessage();
    }
}

// Include header and sidebar
include '../../includes/header.php';
include '../../includes/sidebar.php';
include 'bail_header.php'; // Include bail navigation
?>

<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="mt-4">Report Bail Violation</h1>
            <ol class="breadcrumb mb-4">
                <li class="breadcrumb-item"><a href="../../index.php">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="index.php">Bail Management</a></li>
                <?php if (!$case_selection): ?>
                <li class="breadcrumb-item"><a href="view_case.php?id=<?php echo $blotter_id; ?>">Case Details</a></li>
                <?php endif; ?>
                <li class="breadcrumb-item active">Report Violation</li>
            </ol>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php 
                    echo $_SESSION['success']; 
                    unset($_SESSION['success']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php 
                    echo $_SESSION['error']; 
                    unset($_SESSION['error']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if ($case_selection): ?>
            <!-- Case Selection Card -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    🔍 Select Case to Report Violation
                </div>
                <div class="card-body">
                    <?php if (empty($active_cases)): ?>
                        <div class="alert alert-info">
                            ℹ️ No cases currently on bail. Violations can only be reported for cases on bail status.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="caseSelectionTable">
                                <thead>
                                    <tr>
                                        <th>Reference #</th>
                                        <th>Suspect Name</th>
                                        <th>Case Type</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($active_cases as $case): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($case['reference_number']); ?></td>
                                        <td><?php echo htmlspecialchars($case['suspect_name']); ?></td>
                                        <td><?php echo htmlspecialchars($case['case_type']); ?></td>
                                        <td><?php echo getStatusBadge($case['status']); ?></td>
                                        <td>
                                            <a href="report_violation.php?id=<?php echo $case['blotter_id']; ?>" class="btn btn-sm btn-danger">
                                                ⚠️ Report Violation
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php else: ?>
            <!-- Case Information Card -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    📋 Case Information
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Reference Number:</strong> 🔢 <?php echo htmlspecialchars($case['reference_number']); ?></p>
                            <p><strong>Suspect Name:</strong> 🧑 <?php echo htmlspecialchars($case['suspect_name']); ?></p>
                            <p><strong>Case Type:</strong> 📂 <?php echo htmlspecialchars($case['case_type']); ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Status:</strong> <?php echo getStatusBadge($case['status']); ?></p>
                            <p><strong>Bail Amount:</strong> 💰 ₱<?php echo number_format($case['bail_amount'], 2); ?></p>
                            <p><strong>Released Date:</strong> 📅 <?php echo !empty($case['release_date']) ? date('F d, Y', strtotime($case['release_date'])) : 'N/A'; ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Report Violation Form -->
            <div class="card mb-4">
                <div class="card-header bg-danger text-white">
                    ⚠️ Report Bail Violation
                </div>
                <div class="card-body">
                    <form method="POST" action="report_violation.php" enctype="multipart/form-data" id="violationForm">
                        <input type="hidden" name="blotter_id" value="<?php echo $blotter_id; ?>">
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="violation_date" class="form-label">📅 Violation Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="violation_date" name="violation_date" value="<?php echo date('Y-m-d'); ?>" max="<?php echo date('Y-m-d'); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="violation_type" class="form-label">🏷️ Violation Type <span class="text-danger">*</span></label>
                                <select class="form-select" id="violation_type" name="violation_type" required>
                                    <option value="">-- Select Violation Type --</option>
                                    <option value="Missed Hearing">Missed Court Hearing</option>
                                    <option value="Condition Breach">Breach of Bail Conditions</option>
                                    <option value="Contact Violation">Contact with Victim/Witness</option>
                                    <option value="Travel Restriction">Travel Restriction Violation</option>
                                    <option value="Other">Other Violation</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="violation_details" class="form-label">📝 Violation Details <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="violation_details" name="violation_details" rows="5" required placeholder="Provide detailed information about the violation..."></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="evidence_files" class="form-label">📎 Evidence Files (Images, Documents)</label>
                            <input class="form-control" type="file" id="evidence_files" name="evidence_files[]" multiple accept=".jpg,.jpeg,.png,.pdf,.doc,.docx">
                            <div class="form-text">You can upload multiple files. Accepted formats: JPG, PNG, PDF, DOC, DOCX. Max size: 5MB per file.</div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <a href="view_case.php?id=<?php echo $blotter_id; ?>" class="btn btn-secondary">
                                ↩️ Back to Case
                            </a>
                            <button type="submit" name="report_violation" class="btn btn-danger">
                                ⚠️ Report Violation
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Previous Violations -->
            <?php if (!empty($violations)): ?>
            <div class="card mb-4">
                <div class="card-header">
                    🕒 Previous Violations
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Reported By</th>
                                    <th>Details</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($violations as $violation): ?>
                                <tr>
                                    <td><?php echo date('M d, Y', strtotime($violation['violation_date'])); ?></td>
                                    <td><?php echo htmlspecialchars($violation['violation_type']); ?></td>
                                    <td>
                                        <?php 
                                        switch ($violation['status']) {
                                            case 'Reported':
                                                echo '<span class="badge bg-warning">Reported</span>';
                                                break;
                                            case 'In Process':
                                                echo '<span class="badge bg-primary">In Process</span>';
                                                break;
                                            case 'Resolved':
                                                echo '<span class="badge bg-success">Resolved</span>';
                                                break;
                                            case 'Dismissed':
                                                echo '<span class="badge bg-secondary">Dismissed</span>';
                                                break;
                                            default:
                                                echo '<span class="badge bg-secondary">' . $violation['status'] . '</span>';
                                        }
                                        ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($violation['reported_by_name']); ?></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#violationModal<?php echo $violation['violation_id']; ?>">
                                            👁️ View
                                        </button>
                                        <?php if ($violation['status'] == 'Reported'): ?>
                                        <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#violationModal<?php echo $violation['violation_id']; ?>">
                                            ✅ Resolve
                                        </button>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                
                                <!-- Violation Details Modal -->
                                <div class="modal fade" id="violationModal<?php echo $violation['violation_id']; ?>" tabindex="-1" aria-labelledby="violationModalLabel<?php echo $violation['violation_id']; ?>" aria-hidden="true">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="violationModalLabel<?php echo $violation['violation_id']; ?>">🚨 Violation Details</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="mb-3">
                                                    <h6>📋 Violation Information</h6>
                                                    <p><strong>📅 Date:</strong> <?php echo date('F d, Y', strtotime($violation['violation_date'])); ?></p>
                                                    <p><strong>🏷️ Type:</strong> <?php echo htmlspecialchars($violation['violation_type']); ?></p>
                                                    <p><strong>👤 Reported By:</strong> <?php echo htmlspecialchars($violation['reported_by_name']); ?> on <?php echo date('F d, Y h:i A', strtotime($violation['reported_at'])); ?></p>
                                                    <p><strong>🚦 Status:</strong> 
                                                        <?php 
                                                        switch ($violation['status']) {
                                                            case 'Reported':
                                                                echo '<span class="badge bg-warning">Reported</span>';
                                                                break;
                                                            case 'In Process':
                                                                echo '<span class="badge bg-primary">In Process</span>';
                                                                break;
                                                            case 'Resolved':
                                                                echo '<span class="badge bg-success">Resolved</span>';
                                                                break;
                                                            case 'Dismissed':
                                                                echo '<span class="badge bg-secondary">Dismissed</span>';
                                                                break;
                                                            default:
                                                                echo '<span class="badge bg-secondary">' . $violation['status'] . '</span>';
                                                        }
                                                        ?>
                                                    </p>
                                                </div>
                                                
                                                <div class="mb-3">
                                                    <h6>📝 Details</h6>
                                                    <div class="p-3 bg-light rounded">
                                                        <?php echo nl2br(htmlspecialchars($violation['violation_details'])); ?>
                                                    </div>
                                                </div>
                                                
                                                <?php if (!empty($violation['evidence_files'])): ?>
                                                <div class="mb-3">
                                                    <h6>📎 Evidence Files</h6>
                                                    <div class="row">
                                                        <?php 
                                                        $files = explode(',', $violation['evidence_files']);
                                                        foreach ($files as $file):
                                                            $file_path = '../../uploads/violations/' . $file;
                                                            $file_ext = pathinfo($file, PATHINFO_EXTENSION);
                                                            $is_image = in_array(strtolower($file_ext), ['jpg', 'jpeg', 'png', 'gif']);
                                                        ?>
                                                        <div class="col-md-3 mb-2">
                                                            <div class="card">
                                                                <?php if ($is_image): ?>
                                                                    <img src="<?php echo $file_path; ?>" class="card-img-top img-thumbnail" alt="Evidence">
                                                                <?php else: ?>
                                                                    <div class="card-img-top text-center py-4 bg-light">
                                                                        📄
                                                                        <p class="mt-2 mb-0">.<?php echo $file_ext; ?></p>
                                                                    </div>
                                                                <?php endif; ?>
                                                                <div class="card-body p-2 text-center">
                                                                    <a href="<?php echo $file_path; ?>" class="btn btn-sm btn-primary" target="_blank">View</a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <?php endforeach; ?>
                                                    </div>
                                                </div>
                                                <?php endif; ?>
                                                
                                                <?php if ($violation['status'] == 'Resolved' || $violation['status'] == 'Dismissed'): ?>
                                                <div class="mb-3">
                                                    <h6>✅ Resolution</h6>
                                                    <p><strong>👤 Resolved By:</strong> <?php echo htmlspecialchars($violation['resolved_by_name']); ?> on <?php echo date('F d, Y h:i A', strtotime($violation['resolved_at'])); ?></p>
                                                    <p><strong>🔨 Actions Taken:</strong></p>
                                                    <div class="p-3 bg-light rounded">
                                                        <?php echo nl2br(htmlspecialchars($violation['action_taken'])); ?>
                                                    </div>
                                                    <p><strong>✅ Resolution:</strong></p>
                                                    <div class="p-3 bg-light rounded">
                                                        <?php echo nl2br(htmlspecialchars($violation['resolution'])); ?>
                                                    </div>
                                                </div>
                                                <?php else: ?>
                                                <!-- Show resolution form for reported violations -->
                                                <div class="mb-3">
                                                    <hr>
                                                    <h6>✅ Resolve this Violation</h6>
                                                    <form method="POST" action="report_violation.php" id="resolveForm<?php echo $violation['violation_id']; ?>">
                                                        <input type="hidden" name="violation_id" value="<?php echo $violation['violation_id']; ?>">
                                                        <input type="hidden" name="blotter_id" value="<?php echo $blotter_id; ?>">
                                                        
                                                        <div class="mb-3">
                                                            <label for="resolution_status<?php echo $violation['violation_id']; ?>" class="form-label">🚦 Resolution Status <span class="text-danger">*</span></label>
                                                            <select class="form-select" id="resolution_status<?php echo $violation['violation_id']; ?>" name="resolution_status" required>
                                                                <option value="">-- Select Status --</option>
                                                                <option value="Resolved">Resolved</option>
                                                                <option value="Dismissed">Dismissed</option>
                                                            </select>
                                                        </div>
                                                        
                                                        <div class="mb-3">
                                                            <label for="action_taken<?php echo $violation['violation_id']; ?>" class="form-label">🔨 Actions Taken <span class="text-danger">*</span></label>
                                                            <textarea class="form-control" id="action_taken<?php echo $violation['violation_id']; ?>" name="action_taken" rows="3" required placeholder="Describe actions taken to address this violation..."></textarea>
                                                        </div>
                                                        
                                                        <div class="mb-3">
                                                            <label for="resolution<?php echo $violation['violation_id']; ?>" class="form-label">📝 Resolution Details <span class="text-danger">*</span></label>
                                                            <textarea class="form-control" id="resolution<?php echo $violation['violation_id']; ?>" name="resolution" rows="3" required placeholder="Provide details about the resolution..."></textarea>
                                                        </div>
                                                        
                                                        <button type="submit" name="resolve_violation" class="btn btn-success">
                                                            ✅ Submit Resolution
                                                        </button>
                                                    </form>
                                                </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            <?php endif; ?>
        </main>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize datatable for case selection
    if (document.getElementById('caseSelectionTable')) {
        $('#caseSelectionTable').DataTable({
            responsive: true,
            order: [[0, 'desc']]
        });
    }
    
    // Form validation
    const violationForm = document.getElementById('violationForm');
    if (violationForm) {
        violationForm.addEventListener('submit', function(event) {
            const violationType = document.getElementById('violation_type').value;
            const violationDetails = document.getElementById('violation_details').value;
            
            if (!violationType || !violationDetails.trim()) {
                event.preventDefault();
                alert('Please complete all required fields.');
                return false;
            }
            
            // File size validation
            const evidenceFiles = document.getElementById('evidence_files').files;
            if (evidenceFiles.length > 0) {
                for (let i = 0; i < evidenceFiles.length; i++) {
                    if (evidenceFiles[i].size > 5 * 1024 * 1024) { // 5MB
                        event.preventDefault();
                        alert('File size exceeds 5MB limit: ' + evidenceFiles[i].name);
                        return false;
                    }
                }
            }
        });
    }
    
    // Resolution form validation
    const resolveForms = document.querySelectorAll('[id^="resolveForm"]');
    resolveForms.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            const status = form.querySelector('[name="resolution_status"]').value;
            const actionTaken = form.querySelector('[name="action_taken"]').value;
            const resolution = form.querySelector('[name="resolution"]').value;
            
            if (!status || !actionTaken.trim() || !resolution.trim()) {
                event.preventDefault();
                alert('Please complete all required fields for resolution.');
                return false;
            }
        });
    });
});
</script>

<?php
include '../../includes/footer.php';
?> 