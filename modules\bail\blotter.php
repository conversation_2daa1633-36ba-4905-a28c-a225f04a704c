<?php
// Include database connection - only once
include_once '../../includes/session.php';

// Include header and sidebar first to prevent header errors
include_once '../../includes/header.php';
include_once '../../includes/sidebar.php';
include_once 'bail_header.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    $_SESSION['error'] = 'You must be logged in to access the Blotter Entries page.';
    echo "<script>window.location.href='../../login.php';</script>";
    exit();
}

// Include permission functions
include_once '../../includes/functions/permission_functions.php';

// Check if user has permission to access bail module
if (!canAccessModule('bail')) {
    $_SESSION['error'] = 'You do not have permission to access the Bail Management module.';
    echo "<script>window.location.href='../../index.php';</script>";
    exit();
}

// Set page title
$page_title = 'Blotter Entries';

// Handle search and filters
$search = isset($_GET['search']) ? $_GET['search'] : '';
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';

// Build WHERE clause for filtering
$where_clauses = [];
$params = [];

if (!empty($search)) {
    $where_clauses[] = "(reference_number LIKE :search OR suspect_name LIKE :search OR case_type LIKE :search OR complainant_name LIKE :search)";
    $params[':search'] = '%' . $search . '%';
}

if (!empty($status_filter)) {
    $where_clauses[] = "status = :status";
    $params[':status'] = $status_filter;
}

if (!empty($date_from)) {
    $where_clauses[] = "DATE(incident_date) >= :date_from";
    $params[':date_from'] = $date_from;
}

if (!empty($date_to)) {
    $where_clauses[] = "DATE(incident_date) <= :date_to";
    $params[':date_to'] = $date_to;
}

$where_sql = !empty($where_clauses) ? 'WHERE ' . implode(' AND ', $where_clauses) : '';

// Get blotter entries with pagination
$current_page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$records_per_page = 10;
$offset = ($current_page - 1) * $records_per_page;

try {
    // Count total records for pagination
    $count_sql = "SELECT COUNT(*) AS total FROM blotter_entries " . $where_sql;
    $count_stmt = $conn->prepare($count_sql);
    foreach ($params as $param_key => $param_value) {
        $count_stmt->bindValue($param_key, $param_value);
    }
    $count_stmt->execute();
    $total_records = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
    $total_pages = ceil($total_records / $records_per_page);

    // Get blotter entries
    $sql = "
        SELECT be.*, ba.is_bailable, ba.bail_amount, 
               (SELECT COUNT(*) FROM bail_payments bp WHERE bp.blotter_id = be.blotter_id AND bp.payment_status = 'Completed') as payment_count,
               (SELECT COUNT(*) FROM court_hearings ch WHERE ch.blotter_id = be.blotter_id AND ch.hearing_date > NOW()) as upcoming_hearings
        FROM blotter_entries be
        LEFT JOIN bail_assessments ba ON be.blotter_id = ba.blotter_id
        $where_sql
        ORDER BY be.created_at DESC
        LIMIT :offset, :limit
    ";
    
    $stmt = $conn->prepare($sql);
    foreach ($params as $param_key => $param_value) {
        $stmt->bindValue($param_key, $param_value);
    }
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindValue(':limit', $records_per_page, PDO::PARAM_INT);
    $stmt->execute();
    $blotter_entries = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error: ' . $e->getMessage();
    $blotter_entries = [];
    $total_pages = 0;
}
?>

<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="mt-4">📋 Blotter Entries</h1>
            <ol class="breadcrumb mb-4">
                <li class="breadcrumb-item"><a href="../../index.php">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="index.php">Bail Management</a></li>
                <li class="breadcrumb-item active">Blotter Entries</li>
            </ol>

            <!-- Search and Filters -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">🔍 Search and Filters</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="blotter.php" class="row g-3">
                        <div class="col-md-4">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="search" name="search" placeholder="Search..." value="<?php echo htmlspecialchars($search); ?>">
                                <label for="search">Search (Reference #, Name, Case Type)</label>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="status" name="status">
                                    <option value="" <?php echo $status_filter === '' ? 'selected' : ''; ?>>All Statuses</option>
                                    <option value="Active" <?php echo $status_filter === 'Active' ? 'selected' : ''; ?>>Active</option>
                                    <option value="On Bail" <?php echo $status_filter === 'On Bail' ? 'selected' : ''; ?>>On Bail</option>
                                    <option value="Closed" <?php echo $status_filter === 'Closed' ? 'selected' : ''; ?>>Closed</option>
                                    <option value="Transferred" <?php echo $status_filter === 'Transferred' ? 'selected' : ''; ?>>Transferred</option>
                                    <option value="Dismissed" <?php echo $status_filter === 'Dismissed' ? 'selected' : ''; ?>>Dismissed</option>
                                </select>
                                <label for="status">Status</label>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-floating mb-3">
                                <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo htmlspecialchars($date_from); ?>">
                                <label for="date_from">Date From</label>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-floating mb-3">
                                <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo htmlspecialchars($date_to); ?>">
                                <label for="date_to">Date To</label>
                            </div>
                        </div>
                        <div class="col-md-2 d-flex align-items-center">
                            <button type="submit" class="btn btn-primary me-2">
                                🔍 Apply
                            </button>
                            <a href="blotter.php" class="btn btn-secondary">🔄 Reset</a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Blotter Entries List -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">📋 Blotter Entries</h5>
                    </div>
                    <a href="new_blotter.php" class="btn btn-success">
                        ➕ New Blotter Entry
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>🔢 Ref #</th>
                                    <th>👨‍⚖️ Suspect</th>
                                    <th>👥 Complainant</th>
                                    <th>📝 Case Type</th>
                                    <th>📅 Incident Date</th>
                                    <th>🚦 Status</th>
                                    <th>💵 Bail Status</th>
                                    <th>⚙️ Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($blotter_entries)): ?>
                                    <tr>
                                        <td colspan="8" class="text-center">No blotter entries found</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($blotter_entries as $entry): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($entry['reference_number']); ?></td>
                                            <td><?php echo htmlspecialchars($entry['suspect_name']); ?></td>
                                            <td><?php echo htmlspecialchars($entry['complainant_name']); ?></td>
                                            <td><?php echo htmlspecialchars($entry['case_type']); ?></td>
                                            <td><?php echo date('M d, Y', strtotime($entry['incident_date'])); ?></td>
                                            <td>
                                                <?php 
                                                $status_class = '';
                                                switch ($entry['status']) {
                                                    case 'Active':
                                                        $status_class = 'bg-success';
                                                        break;
                                                    case 'On Bail':
                                                        $status_class = 'bg-success';
                                                        break;
                                                    case 'Closed':
                                                        $status_class = 'bg-secondary';
                                                        break;
                                                    case 'Transferred':
                                                        $status_class = 'bg-info';
                                                        break;
                                                    case 'Dismissed':
                                                        $status_class = 'bg-danger';
                                                        break;
                                                    default:
                                                        $status_class = 'bg-secondary';
                                                }
                                                ?>
                                                <span class="badge <?php echo $status_class; ?> badge-emoji">
                                                    <?php 
                                                    $emoji = '';
                                                    switch ($entry['status']) {
                                                        case 'Active':
                                                            $emoji = '✅';
                                                            break;
                                                        case 'On Bail':
                                                            $emoji = '✅';
                                                            break;
                                                        case 'Closed':
                                                            $emoji = '🔒';
                                                            break;
                                                        case 'Transferred':
                                                            $emoji = '🔄';
                                                            break;
                                                        case 'Dismissed':
                                                            $emoji = '❌';
                                                            break;
                                                    }
                                                    echo $emoji . ' ' . htmlspecialchars($entry['status']); 
                                                    ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if (isset($entry['is_bailable']) && $entry['is_bailable'] == 1): ?>
                                                    <?php if ($entry['payment_count'] > 0): ?>
                                                        <span class="badge bg-success badge-emoji">💰 Paid</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-warning badge-emoji">⏳ Bail Set: ₱<?php echo number_format($entry['bail_amount'], 2); ?></span>
                                                    <?php endif; ?>
                                                <?php elseif (isset($entry['is_bailable']) && $entry['is_bailable'] == 0): ?>
                                                    <span class="badge bg-danger badge-emoji">🚫 Non-Bailable</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary badge-emoji">⌛ Pending Assessment</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="d-flex gap-1">
                                                    <a href="view_case.php?id=<?php echo $entry['blotter_id']; ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="View Case Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="edit_blotter.php?id=<?php echo $entry['blotter_id']; ?>" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Edit Entry">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php if ($entry['status'] == 'Active' && (!isset($entry['is_bailable']) || $entry['is_bailable'] === null)): ?>
                                                        <a href="bail_assessment.php?id=<?php echo $entry['blotter_id']; ?>" class="btn btn-sm btn-secondary" data-bs-toggle="tooltip" title="Assess for Bail">
                                                            <i class="fas fa-balance-scale"></i>
                                                        </a>
                                                    <?php elseif ($entry['status'] == 'Active' && $entry['is_bailable'] == 1 && $entry['payment_count'] == 0): ?>
                                                        <a href="bail_payment.php?id=<?php echo $entry['blotter_id']; ?>" class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="Process Bail Payment">
                                                            <i class="fas fa-money-bill-wave"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <a href="schedule_hearing.php?id=<?php echo $entry['blotter_id']; ?>" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="Schedule Hearing">
                                                        <i class="fas fa-calendar"></i>
                                                    </a>
                                                    <?php if ($entry['upcoming_hearings'] > 0): ?>
                                                        <a href="hearings.php?blotter_id=<?php echo $entry['blotter_id']; ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="View Hearings">
                                                            <i class="fas fa-gavel"></i> <?php echo $entry['upcoming_hearings']; ?>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if ($_SESSION['user_type'] == 'Admin' || $_SESSION['user_type'] == 'Barangay Captain'): ?>
                                                        <button type="button" class="btn btn-sm btn-danger delete-entry" data-id="<?php echo $entry['blotter_id']; ?>" data-ref="<?php echo htmlspecialchars($entry['reference_number']); ?>" data-bs-toggle="tooltip" title="Delete Entry">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center mt-4">
                                <li class="page-item <?php echo ($current_page <= 1) ? 'disabled' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $current_page - 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                    <li class="page-item <?php echo ($current_page == $i) ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>
                                <li class="page-item <?php echo ($current_page >= $total_pages) ? 'disabled' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $current_page + 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../../includes/footer.php'; ?>

<!-- Toast container -->
<div class="toast-container position-fixed top-0 end-0 p-3">
    <div id="successToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex align-items-center p-3">
            <div class="me-2">✅</div>
            <div class="me-auto"><?php echo isset($_SESSION['success']) ? $_SESSION['success'] : (isset($_SESSION['error']) ? $_SESSION['error'] : ''); ?></div>
            <div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the blotter entry <span id="refNumber" class="fw-bold"></span>?</p>
                <p class="text-danger"><i class="fas fa-exclamation-triangle me-2"></i>This action cannot be undone and will remove all associated records including bail assessments, payments, and hearing schedules.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" action="delete_blotter.php">
                    <input type="hidden" name="blotter_id" id="blotterId" value="">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Delete confirmation
    document.addEventListener('DOMContentLoaded', function() {
        const deleteButtons = document.querySelectorAll('.delete-entry');
        
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const ref = this.getAttribute('data-ref');
                
                document.getElementById('blotterId').value = id;
                document.getElementById('refNumber').textContent = ref;
                
                const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
                deleteModal.show();
            });
        });

        // Show toast notification if there's a message
        <?php if(isset($_SESSION['success']) || isset($_SESSION['error'])): ?>
        // Update toast styling based on message type
        const toast = document.getElementById('successToast');
        
        <?php if(isset($_SESSION['success'])): ?>
        toast.style.backgroundColor = '#28a745'; // success green
        toast.style.color = 'white';
        <?php elseif(isset($_SESSION['error'])): ?>
        toast.style.backgroundColor = '#dc3545'; // danger red
        toast.style.color = 'white';
        <?php endif; ?>
        
        const successToast = new bootstrap.Toast(toast, {
            delay: 5000
        });
        successToast.show();

        <?php 
        // Clear session messages after displaying
        if(isset($_SESSION['success'])) unset($_SESSION['success']);
        if(isset($_SESSION['error'])) unset($_SESSION['error']);
        ?>
        <?php endif; ?>
    });
</script>