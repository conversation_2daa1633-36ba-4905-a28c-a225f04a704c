<?php
// Include database connection
include '../../includes/session.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include permission functions
include_once '../../includes/functions/permission_functions.php';

// Check if user has permission to access bail module
if (!canAccessModule('bail')) {
    $_SESSION['error'] = 'You do not have permission to access this page.';
    header('Location: ../../index.php');
    exit();
}

// Set page title
$page_title = 'Edit Court Hearing';

// Check if a hearing ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = 'No hearing specified to edit.';
    header('Location: hearings.php');
    exit();
}

$hearing_id = $_GET['id'];

// Fetch hearing details with blotter and user information
try {
    $stmt = $conn->prepare("
        SELECT ch.*, 
               be.reference_number, be.suspect_name, be.case_type, be.status as case_status,
               u1.username as recorded_by_name
        FROM court_hearings ch
        JOIN blotter_entries be ON ch.blotter_id = be.blotter_id
        LEFT JOIN users u1 ON ch.recorded_by = u1.user_id
        WHERE ch.hearing_id = :hearing_id
    ");
    $stmt->bindParam(':hearing_id', $hearing_id);
    $stmt->execute();
    $hearing = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$hearing) {
        $_SESSION['error'] = 'Hearing not found.';
        header('Location: hearings.php');
        exit();
    }

    // If hearing status is not 'Scheduled', redirect
    if ($hearing['status'] !== 'Scheduled') {
        $_SESSION['error'] = 'Only scheduled hearings can be edited.';
        header('Location: view_hearing.php?id=' . $hearing_id);
        exit();
    }

    // Get case information
    $blotter_id = $hearing['blotter_id'];

} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error: ' . $e->getMessage();
    header('Location: hearings.php');
    exit();
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $hearing_date = $_POST['hearing_date'];
        $hearing_type = $_POST['hearing_type'];
        $hearing_location = $_POST['hearing_location'];
        $attendance_required = isset($_POST['attendance_required']) ? 1 : 0;
        $remarks = $_POST['hearing_details'] ?? '';

        // Update court hearing
        $sql = "UPDATE court_hearings SET 
                hearing_date = :hearing_date, 
                hearing_type = :hearing_type, 
                hearing_location = :hearing_location, 
                attendance_required = :attendance_required,
                hearing_details = :remarks
            WHERE hearing_id = :hearing_id";
        
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':hearing_id', $hearing_id);
        $stmt->bindParam(':hearing_date', $hearing_date);
        $stmt->bindParam(':hearing_type', $hearing_type);
        $stmt->bindParam(':hearing_location', $hearing_location);
        $stmt->bindParam(':attendance_required', $attendance_required);
        $stmt->bindParam(':remarks', $remarks);
        
        if ($stmt->execute()) {
            $_SESSION['success'] = 'Court hearing updated successfully.';
            
            // Redirect to hearings page if requested
            if (isset($_POST['return_to_hearings']) && $_POST['return_to_hearings'] == '1') {
                header('Location: hearings.php?blotter_id=' . $blotter_id);
            } else {
                header('Location: view_hearing.php?id=' . $hearing_id);
            }
            exit();
        } else {
            $_SESSION['error'] = 'Failed to update court hearing.';
        }
    } catch (PDOException $e) {
        $_SESSION['error'] = 'Database error: ' . $e->getMessage();
    }
}

// Include header and sidebar
include '../../includes/header.php';
include '../../includes/sidebar.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="mt-4">📝 Edit Court Hearing</h1>
            <ol class="breadcrumb mb-4">
                <li class="breadcrumb-item"><a href="../../index.php">🏠 Dashboard</a></li>
                <li class="breadcrumb-item"><a href="index.php">⚖️ Bail Management</a></li>
                <li class="breadcrumb-item"><a href="hearings.php">📅 Court Hearings</a></li>
                <li class="breadcrumb-item active">📝 Edit Hearing</li>
            </ol>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    ✅ <?php 
                    echo $_SESSION['success']; 
                    unset($_SESSION['success']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    ⚠️ <?php 
                    echo $_SESSION['error']; 
                    unset($_SESSION['error']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <!-- Case Information Card -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        📋 Case Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>📄 Reference Number:</strong> <?php echo htmlspecialchars($hearing['reference_number']); ?></p>
                            <p><strong>📁 Case Type:</strong> <?php echo htmlspecialchars($hearing['case_type']); ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>👤 Suspect Name:</strong> <?php echo htmlspecialchars($hearing['suspect_name']); ?></p>
                            <p><strong>🚦 Status:</strong> <?php echo getBadgeHtml($hearing['case_status']); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Edit Hearing Form Card -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        ✏️ Edit Hearing Details
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="edit_hearing.php?id=<?php echo $hearing_id; ?>">
                        <input type="hidden" name="blotter_id" value="<?php echo $blotter_id; ?>">
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="hearing_date" class="form-label">🗓️ Hearing Date and Time</label>
                                <input type="datetime-local" class="form-control" id="hearing_date" name="hearing_date" value="<?php echo date('Y-m-d\TH:i', strtotime($hearing['hearing_date'])); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="hearing_type" class="form-label">🔍 Hearing Type</label>
                                <select class="form-select" id="hearing_type" name="hearing_type" required>
                                    <option value="">-- Select Type --</option>
                                    <option value="Initial Appearance" <?php echo ($hearing['hearing_type'] == 'Initial Appearance') ? 'selected' : ''; ?>>🏛️ Initial Appearance</option>
                                    <option value="Arraignment" <?php echo ($hearing['hearing_type'] == 'Arraignment') ? 'selected' : ''; ?>>📣 Arraignment</option>
                                    <option value="Preliminary Hearing" <?php echo ($hearing['hearing_type'] == 'Preliminary Hearing') ? 'selected' : ''; ?>>🔎 Preliminary Hearing</option>
                                    <option value="Pretrial Conference" <?php echo ($hearing['hearing_type'] == 'Pretrial Conference') ? 'selected' : ''; ?>>👥 Pretrial Conference</option>
                                    <option value="Trial" <?php echo ($hearing['hearing_type'] == 'Trial') ? 'selected' : ''; ?>>⚖️ Trial</option>
                                    <option value="Sentencing" <?php echo ($hearing['hearing_type'] == 'Sentencing') ? 'selected' : ''; ?>>🧑‍⚖️ Sentencing</option>
                                    <option value="Follow-up Hearing" <?php echo ($hearing['hearing_type'] == 'Follow-up Hearing') ? 'selected' : ''; ?>>🔄 Follow-up Hearing</option>
                                    <option value="Other" <?php echo ($hearing['hearing_type'] == 'Other') ? 'selected' : ''; ?>>📌 Other</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label for="hearing_location" class="form-label">📍 Hearing Location</label>
                                <input type="text" class="form-control" id="hearing_location" name="hearing_location" value="<?php echo htmlspecialchars($hearing['hearing_location']); ?>" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="attendance_required" name="attendance_required" <?php echo $hearing['attendance_required'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="attendance_required">
                                    ✋ Attendance Required
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="hearing_details" class="form-label">📝 Remarks/Details</label>
                            <textarea class="form-control" id="hearing_details" name="hearing_details" rows="3"><?php echo htmlspecialchars($hearing['hearing_details'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="return_to_hearings" name="return_to_hearings" value="1">
                                <label class="form-check-label" for="return_to_hearings">
                                    🔙 Return to hearings list after saving
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="view_hearing.php?id=<?php echo $hearing_id; ?>" class="btn btn-secondary me-md-2">❌ Cancel</a>
                            <button type="submit" class="btn btn-primary">💾 Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- JavaScript for changing location based on hearing type -->
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const hearingTypeSelect = document.getElementById('hearing_type');
                    const locationInput = document.getElementById('hearing_location');
                    
                    // Only set default locations if location is empty
                    hearingTypeSelect.addEventListener('change', function() {
                        if (locationInput.value === '') {
                            const selectedType = this.value;
                            let defaultLocation = '';
                            
                            switch(selectedType) {
                                case 'Initial Appearance':
                                case 'Arraignment':
                                    defaultLocation = 'Barangay Hall - Justice Room';
                                    break;
                                case 'Preliminary Hearing':
                                    defaultLocation = 'Municipal Trial Court';
                                    break;
                                case 'Pretrial Conference':
                                case 'Trial':
                                    defaultLocation = 'Regional Trial Court';
                                    break;
                                case 'Sentencing':
                                    defaultLocation = 'Court of First Instance';
                                    break;
                                default:
                                    defaultLocation = '';
                            }
                            
                            locationInput.value = defaultLocation;
                        }
                    });
                });
            </script>
        </main>
    </div>
</div>

<?php 
// Helper function to display status badges
function getBadgeHtml($status) {
    $badge_class = '';
    $emoji = '';
    switch ($status) {
        case 'Active':
            $badge_class = 'bg-success';
            $emoji = '🟢 ';
            break;
        case 'Resolved':
            $badge_class = 'bg-success';
            $emoji = '✅ ';
            break;
        case 'On Bail':
            $badge_class = 'bg-warning';
            $emoji = '⚠️ ';
            break;
        case 'Closed':
            $badge_class = 'bg-secondary';
            $emoji = '🔒 ';
            break;
        default:
            $badge_class = 'bg-info';
            $emoji = 'ℹ️ ';
    }
    
    return '<span class="badge ' . $badge_class . '">' . $emoji . $status . '</span>';
}

include '../../includes/footer.php'; 
?> 