<?php
// Include database connection
include '../../includes/session.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include permission functions
include_once '../../includes/functions/permission_functions.php';

// Check if user has permission to access bail module
if (!canAccessModule('bail')) {
    $_SESSION['error'] = 'You do not have permission to access this page.';
    header('Location: ../../index.php');
    exit();
}

// Set page title
$page_title = 'View Case';

// Check if a blotter ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = 'No case specified to view.';
    header('Location: blotter.php');
    exit();
}

$blotter_id = $_GET['id'];

// Fetch case details
try {
    $stmt = $conn->prepare("
        SELECT be.*, ba.is_bailable, ba.bail_amount, ba.legal_basis, ba.remarks as assessment_remarks,
               ba.assessment_date as assessed_at, u1.username as assessor_name,
               (SELECT SUM(amount) FROM bail_payments WHERE blotter_id = be.blotter_id AND payment_status = 'Completed') as total_paid,
               (SELECT ro.release_id FROM release_orders ro WHERE ro.blotter_id = be.blotter_id LIMIT 1) as release_order_id,
               (SELECT ro.release_status FROM release_orders ro WHERE ro.blotter_id = be.blotter_id LIMIT 1) as release_status,
               u2.username as created_by_name
        FROM blotter_entries be
        LEFT JOIN bail_assessments ba ON be.blotter_id = ba.blotter_id
        LEFT JOIN users u1 ON ba.recommended_by = u1.user_id
        LEFT JOIN users u2 ON be.created_by = u2.user_id
        WHERE be.blotter_id = :blotter_id
    ");
    $stmt->bindParam(':blotter_id', $blotter_id);
    $stmt->execute();
    $case = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$case) {
        $_SESSION['error'] = 'Case not found.';
        header('Location: blotter.php');
        exit();
    }

    // Get bail payments
    $stmt = $conn->prepare("
        SELECT bp.*, u.username 
        FROM bail_payments bp
        LEFT JOIN users u ON bp.received_by = u.user_id
        WHERE bp.blotter_id = :blotter_id
        ORDER BY bp.payment_date DESC
    ");
    $stmt->bindParam(':blotter_id', $blotter_id);
    $stmt->execute();
    $payments = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get court hearings
    $stmt = $conn->prepare("
        SELECT ch.*, u.username as created_by_name
        FROM court_hearings ch
        LEFT JOIN users u ON ch.recorded_by = u.user_id
        WHERE ch.blotter_id = :blotter_id
        ORDER BY ch.hearing_date ASC
    ");
    $stmt->bindParam(':blotter_id', $blotter_id);
    $stmt->execute();
    $hearings = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Calculate bail stats
    $total_paid = $case['total_paid'] ?: 0;
    $bail_amount = $case['bail_amount'] ?: 0;
    $remaining_balance = $bail_amount - $total_paid;
    $percent_paid = ($bail_amount > 0) ? min(100, ($total_paid / $bail_amount) * 100) : 0;

} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error: ' . $e->getMessage();
    header('Location: blotter.php');
    exit();
}

// Include header and sidebar
include '../../includes/header.php';
include '../../includes/sidebar.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="mt-4">View Case Details</h1>
            <ol class="breadcrumb mb-4">
            <li class="breadcrumb-item"><a href="../../index.php">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="index.php">Bail Management</a></li>
                <li class="breadcrumb-item"><a href="blotter.php">Blotter Entries</a></li>
                <li class="breadcrumb-item active">Case Details</li>
            </ol>

            <!-- Action Buttons -->
            <div class="mb-4">
                <a href="blotter.php" class="btn btn-secondary me-2">
                    ↩️ Back to List
                </a>
                <?php if (!isset($case['is_bailable'])): ?>
                    <a href="bail_assessment.php?id=<?php echo $blotter_id; ?>" class="btn btn-primary me-2">
                        ⚖️ Assess for Bail
                    </a>
                <?php elseif ($case['is_bailable'] == 1 && $remaining_balance > 0): ?>
                    <a href="bail_payment.php?id=<?php echo $blotter_id; ?>" class="btn btn-success me-2">
                        💸 Process Payment
                    </a>
                <?php elseif ($case['is_bailable'] == 1 && $remaining_balance <= 0 && (!$case['release_order_id'] || $case['release_status'] != 'Released')): ?>
                    <a href="release_order.php?id=<?php echo $blotter_id; ?>" class="btn btn-warning me-2">
                        📝 Process Release Order
                    </a>
                <?php endif; ?>
                
                <?php if ($case['release_order_id']): ?>
                    <a href="print_release_order.php?id=<?php echo $blotter_id; ?>" class="btn btn-info me-2" target="_blank">
                        🖨️ Print Release Order
                    </a>
                <?php endif; ?>
                
                <a href="schedule_hearing.php?id=<?php echo $blotter_id; ?>" class="btn btn-secondary me-2">
                    📅 Schedule Hearing
                </a>
                
                <a href="report_violation.php?id=<?php echo $blotter_id; ?>" class="btn btn-danger me-2">
                    ⚠️ Report Violation
                </a>
            </div>

            <div class="row">
                <div class="col-xl-8">
                    <!-- Case Information -->
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                📋 Case Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <ul class="list-group">
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <strong>Reference Number:</strong>
                                            <span>🔢 <?php echo htmlspecialchars($case['reference_number']); ?></span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <strong>Case Type:</strong>
                                            <span>📂 <?php echo htmlspecialchars($case['case_type']); ?></span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <strong>Status:</strong>
                                            <span>
                                                <?php 
                                                $status_class = '';
                                                switch ($case['status']) {
                                                    case 'Active':
                                                        $status_class = 'bg-success';
                                                        break;
                                                    case 'On Bail':
                                                        $status_class = 'bg-success';
                                                        break;
                                                    case 'Closed':
                                                        $status_class = 'bg-secondary';
                                                        break;
                                                    case 'Transferred':
                                                        $status_class = 'bg-info';
                                                        break;
                                                    case 'Dismissed':
                                                        $status_class = 'bg-danger';
                                                        break;
                                                    default:
                                                        $status_class = 'bg-secondary';
                                                }
                                                ?>
                                                <span class="badge <?php echo $status_class; ?>"><?php echo htmlspecialchars($case['status']); ?></span>
                                            </span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <strong>Incident Date:</strong>
                                            <span>📅 <?php echo date('M d, Y h:i A', strtotime($case['incident_date'])); ?></span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <strong>Incident Location:</strong>
                                            <span>📍 <?php echo htmlspecialchars($case['incident_location']); ?></span>
                                        </li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-group">
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <strong>Arrest Date:</strong>
                                            <span>🚨 <?php echo date('M d, Y h:i A', strtotime($case['arrest_date'])); ?></span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <strong>Arresting Officer:</strong>
                                            <span>👮 <?php echo htmlspecialchars($case['arresting_officer']); ?></span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <strong>Detention Facility:</strong>
                                            <span>🏢 <?php echo htmlspecialchars($case['detention_facility']); ?></span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <strong>Created By:</strong>
                                            <span>👤 <?php echo htmlspecialchars($case['created_by_name']); ?></span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <strong>Created Date:</strong>
                                            <span>🕒 <?php echo date('M d, Y h:i A', strtotime($case['created_at'])); ?></span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <strong>📝 Incident Details</strong>
                                        </div>
                                        <div class="card-body">
                                            <p class="card-text"><?php echo nl2br(htmlspecialchars($case['incident_details'])); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Suspect and Complainant Info -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-danger text-white">
                                    <h5 class="mb-0">
                                        🧑 Suspect Information
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <ul class="list-group">
                                        <li class="list-group-item">
                                            <strong>Name:</strong> 🧑 <?php echo htmlspecialchars($case['suspect_name']); ?>
                                        </li>
                                        <li class="list-group-item">
                                            <strong>Contact:</strong> 📱 <?php echo htmlspecialchars($case['suspect_contact']); ?>
                                        </li>
                                        <li class="list-group-item">
                                            <strong>Address:</strong> 🏠 <?php echo htmlspecialchars($case['suspect_address']); ?>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">
                                        👤 Complainant Information
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <ul class="list-group">
                                        <li class="list-group-item">
                                            <strong>Name:</strong> 👤 <?php echo htmlspecialchars($case['complainant_name']); ?>
                                        </li>
                                        <li class="list-group-item">
                                            <strong>Contact:</strong> 📱 <?php echo htmlspecialchars($case['complainant_contact']); ?>
                                        </li>
                                        <li class="list-group-item">
                                            <strong>Address:</strong> 🏠 <?php echo htmlspecialchars($case['complainant_address']); ?>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-4">
                    <!-- Bail Status -->
                    <div class="card mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                ⚖️ Bail Status
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (!isset($case['is_bailable'])): ?>
                                <div class="alert alert-warning" role="alert">
                                    ⚠️ This case has not yet been assessed for bail eligibility.
                                </div>
                                <div class="d-grid gap-2">
                                    <a href="bail_assessment.php?id=<?php echo $blotter_id; ?>" class="btn btn-primary">
                                        ⚖️ Assess for Bail
                                    </a>
                                </div>
                            <?php elseif ($case['is_bailable'] == 0): ?>
                                <div class="alert alert-danger" role="alert">
                                    🚫 This case is <strong>NOT eligible for bail</strong> based on assessment.
                                </div>
                                <div class="card bg-light mb-3">
                                    <div class="card-body">
                                        <h6 class="card-title">Assessment Details</h6>
                                        <p><strong>Legal Basis:</strong> <?php echo nl2br(htmlspecialchars($case['legal_basis'])); ?></p>
                                        <?php if (!empty($case['assessment_remarks'])): ?>
                                            <p><strong>Remarks:</strong> <?php echo nl2br(htmlspecialchars($case['assessment_remarks'])); ?></p>
                                        <?php endif; ?>
                                        <p><strong>Assessed By:</strong> <?php echo htmlspecialchars($case['assessor_name']); ?></p>
                                        <p><strong>Assessment Date:</strong> <?php echo date('M d, Y h:i A', strtotime($case['assessed_at'])); ?></p>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-success" role="alert">
                                    ✅ This case is <strong>eligible for bail</strong> based on assessment.
                                </div>
                                
                                <div class="card bg-light mb-3">
                                    <div class="card-body">
                                        <h6 class="card-title">Bail Payment Status</h6>
                                        <div class="row text-center">
                                            <div class="col">
                                                <h6>Total Amount</h6>
                                                <h5 class="text-primary">💰 ₱<?php echo number_format($bail_amount, 2); ?></h5>
                                            </div>
                                            <div class="col">
                                                <h6>Amount Paid</h6>
                                                <h5 class="text-success">💵 ₱<?php echo number_format($total_paid, 2); ?></h5>
                                            </div>
                                        </div>
                                        <div class="progress mt-2 mb-2" style="height: 20px;">
                                            <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $percent_paid; ?>%;" 
                                                 aria-valuenow="<?php echo $percent_paid; ?>" aria-valuemin="0" aria-valuemax="100">
                                                <?php echo round($percent_paid); ?>%
                                            </div>
                                        </div>
                                        <div class="text-center">
                                            <strong>Balance: </strong>
                                            <span class="<?php echo ($remaining_balance > 0) ? 'text-danger' : 'text-success'; ?>">
                                                <?php echo ($remaining_balance > 0) ? '⚠️' : '✓'; ?> ₱<?php echo number_format($remaining_balance, 2); ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                
                                <?php if ($remaining_balance > 0): ?>
                                    <div class="d-grid gap-2 mb-3">
                                        <a href="bail_payment.php?id=<?php echo $blotter_id; ?>" class="btn btn-success">
                                            💸 Process Payment
                                        </a>
                                    </div>
                                <?php elseif ($total_paid > 0): ?>
                                    <div class="card bg-light mb-3">
                                        <div class="card-body">
                                            <h6 class="card-title">Release Status</h6>
                                            <?php if (!$case['release_order_id']): ?>
                                                <div class="alert alert-warning text-center" role="alert">
                                                    📝 Release order has not been processed.
                                                </div>
                                                <div class="d-grid gap-2">
                                                    <a href="release_order.php?id=<?php echo $blotter_id; ?>" class="btn btn-warning">
                                                        📝 Process Release Order
                                                    </a>
                                                </div>
                                            <?php else: ?>
                                                <div class="text-center mb-2">
                                                    <?php 
                                                    $status_class = '';
                                                    switch ($case['release_status']) {
                                                        case 'Pending':
                                                            $status_class = 'bg-warning';
                                                            break;
                                                        case 'Approved':
                                                            $status_class = 'bg-primary';
                                                            break;
                                                        case 'Released':
                                                            $status_class = 'bg-success';
                                                            break;
                                                        case 'Denied':
                                                            $status_class = 'bg-danger';
                                                            break;
                                                        default:
                                                            $status_class = 'bg-secondary';
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $status_class; ?> fs-6">
                                                        <?php echo htmlspecialchars($case['release_status']); ?>
                                                    </span>
                                                </div>
                                                <div class="d-grid gap-2">
                                                    <a href="release_order.php?id=<?php echo $blotter_id; ?>" class="btn btn-primary">
                                                        ✏️ Update Release Status
                                                    </a>
                                                    <a href="print_release_order.php?id=<?php echo $blotter_id; ?>" class="btn btn-info" target="_blank">
                                                        🖨️ Print Release Order
                                                    </a>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Upcoming Hearings -->
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">
                                📅 Upcoming Court Hearings
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($hearings)): ?>
                                <div class="alert alert-info" role="alert">
                                    ℹ️ No court hearings scheduled.
                                </div>
                            <?php else: ?>
                                <ul class="list-group">
                                    <?php 
                                    $upcoming_count = 0;
                                    foreach ($hearings as $hearing): 
                                        if (strtotime($hearing['hearing_date']) > time()):
                                            $upcoming_count++;
                                    ?>
                                        <li class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1"><?php echo date('M d, Y h:i A', strtotime($hearing['hearing_date'])); ?></h6>
                                                <small><?php echo htmlspecialchars($hearing['hearing_type']); ?></small>
                                            </div>
                                            <p class="mb-1"><?php echo htmlspecialchars($hearing['hearing_location']); ?></p>
                                        </li>
                                    <?php 
                                        endif;
                                        if ($upcoming_count >= 3) break; // Show max 3 upcoming hearings
                                    endforeach; 
                                    
                                    if ($upcoming_count == 0):
                                    ?>
                                        <div class="alert alert-info" role="alert">
                                            ℹ️ No upcoming court hearings scheduled.
                                        </div>
                                    <?php endif; ?>
                                </ul>
                                
                                <div class="d-grid gap-2 mt-3">
                                    <a href="hearings.php?blotter_id=<?php echo $blotter_id; ?>" class="btn btn-outline-primary">
                                        📋 View All Hearings
                                    </a>
                                </div>
                            <?php endif; ?>
                            
                            <div class="d-grid gap-2 mt-3">
                                <a href="schedule_hearing.php?id=<?php echo $blotter_id; ?>" class="btn btn-warning">
                                    📆 Schedule New Hearing
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../../includes/footer.php'; ?>

<!-- Toast container -->
<div class="toast-container position-fixed top-0 end-0 p-3">
    <div id="successToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex align-items-center p-3">
            <div class="me-2">✅</div>
            <div class="me-auto"><?php echo isset($_SESSION['success']) ? $_SESSION['success'] : (isset($_SESSION['error']) ? $_SESSION['error'] : ''); ?></div>
            <div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    </div>
</div>

<script>
    // Show toast notification if there's a message
    document.addEventListener('DOMContentLoaded', function() {
        <?php if(isset($_SESSION['success']) || isset($_SESSION['error'])): ?>
        // Update toast styling based on message type
        const toast = document.getElementById('successToast');
        
        <?php if(isset($_SESSION['success'])): ?>
        toast.style.backgroundColor = '#28a745'; // success green
        toast.style.color = 'white';
        <?php elseif(isset($_SESSION['error'])): ?>
        toast.style.backgroundColor = '#dc3545'; // danger red
        toast.style.color = 'white';
        <?php endif; ?>
        
        const successToast = new bootstrap.Toast(toast, {
            delay: 5000
        });
        successToast.show();

        <?php 
        // Clear session messages after displaying
        if(isset($_SESSION['success'])) unset($_SESSION['success']);
        if(isset($_SESSION['error'])) unset($_SESSION['error']);
        ?>
        <?php endif; ?>
    });
</script> 