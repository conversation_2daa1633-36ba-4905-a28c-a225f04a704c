<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/utility.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission - allow access if user has any family relationship permission or edit_residents
if (!hasPermission('add_family_relationship') && !hasPermission('edit_family_relationship') && !hasPermission('delete_family_relationship') && !hasPermission('edit_residents')) {
    header("Location: ../../index.php");
    exit;
}

// Function to get reciprocal relationship
function get_reciprocal_relationship($relationship) {
    $reciprocals = [
        'Father' => 'Child',
        'Mother' => 'Child',
        'Child' => 'Parent',
        'Husband' => 'Wife',
        'Wife' => 'Husband',
        'Brother' => 'Sibling',
        'Sister' => 'Sibling',
        'Sibling' => 'Sibling',
        'Grandfather' => 'Grandchild',
        'Grandmother' => 'Grandchild',
        'Grandchild' => 'Grandparent',
        'Uncle' => 'Nephew/Niece',
        'Aunt' => 'Nephew/Niece',
        'Nephew' => 'Uncle/Aunt',
        'Niece' => 'Uncle/Aunt',
        'Cousin' => 'Cousin',
        'Guardian' => 'Ward',
        'Ward' => 'Guardian',
        'Father-in-law' => 'Son/Daughter-in-law',
        'Mother-in-law' => 'Son/Daughter-in-law',
        'Son-in-law' => 'Father/Mother-in-law',
        'Daughter-in-law' => 'Father/Mother-in-law',
        'Stepfather' => 'Stepchild',
        'Stepmother' => 'Stepchild',
        'Stepchild' => 'Stepparent'
    ];
    
    return isset($reciprocals[$relationship]) ? $reciprocals[$relationship] : null;
}

// Validate that an action was provided
if (!isset($_POST['action'])) {
    header("Location: family_relationships.php?error=invalid_request");
    exit;
}

$action = $_POST['action'];

// Handle different actions
switch ($action) {
    case 'add':
        add_relationship();
        break;
    case 'edit':
        edit_relationship();
        break;
    case 'delete':
        delete_relationship();
        break;
    default:
        header("Location: family_relationships.php?error=invalid_action");
        exit;
}

// Function to add a new relationship
function add_relationship() {
    global $conn;
    
    // Validate form inputs
    $required_fields = ['resident_id', 'relationship', 'related_to'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty($_POST[$field])) {
            header("Location: family_relationships.php?error=validation");
            exit;
        }
    }
    
    $resident_id = (int)$_POST['resident_id'];
    $relationship = htmlspecialchars(trim($_POST['relationship']));
    $related_to = (int)$_POST['related_to'];
    $create_reciprocal = isset($_POST['create_reciprocal']) ? true : false;
    
    // Validate that the two residents are different
    if ($resident_id === $related_to) {
        header("Location: family_relationships.php?error=same_resident");
        exit;
    }
    
    try {
        // Begin transaction
        $conn->beginTransaction();
        
        // Check if relationship already exists
        $check_query = "SELECT * FROM family_relationships 
                       WHERE (resident_id = :resident_id1 AND related_to = :related_to1)
                       OR (resident_id = :resident_id2 AND related_to = :related_to2 AND relationship = :reciprocal)";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bindParam(':resident_id1', $resident_id, PDO::PARAM_INT);
        $check_stmt->bindParam(':related_to1', $related_to, PDO::PARAM_INT);
        $check_stmt->bindParam(':resident_id2', $related_to, PDO::PARAM_INT);
        $check_stmt->bindParam(':related_to2', $resident_id, PDO::PARAM_INT);
        $reciprocal = get_reciprocal_relationship($relationship);
        $check_stmt->bindParam(':reciprocal', $reciprocal, PDO::PARAM_STR);
        $check_stmt->execute();
        
        if ($check_stmt->rowCount() > 0) {
            // Rollback and return error
            $conn->rollBack();
            header("Location: family_relationships.php?error=duplicate");
            exit;
        }
        
        // Insert primary relationship
        $insert_query = "INSERT INTO family_relationships (resident_id, related_to, relationship) 
                        VALUES (:resident_id, :related_to, :relationship)";
        $insert_stmt = $conn->prepare($insert_query);
        $insert_stmt->bindParam(':resident_id', $resident_id, PDO::PARAM_INT);
        $insert_stmt->bindParam(':related_to', $related_to, PDO::PARAM_INT);
        $insert_stmt->bindParam(':relationship', $relationship, PDO::PARAM_STR);
        $insert_stmt->execute();
        
        $relationship_id = $conn->lastInsertId();
        
        // Create reciprocal relationship if requested and a reciprocal exists
        if ($create_reciprocal && $reciprocal) {
            $reciprocal_query = "INSERT INTO family_relationships (resident_id, related_to, relationship) 
                               VALUES (:resident_id, :related_to, :relationship)";
            $reciprocal_stmt = $conn->prepare($reciprocal_query);
            $reciprocal_stmt->bindParam(':resident_id', $related_to, PDO::PARAM_INT);
            $reciprocal_stmt->bindParam(':related_to', $resident_id, PDO::PARAM_INT);
            $reciprocal_stmt->bindParam(':relationship', $reciprocal, PDO::PARAM_STR);
            $reciprocal_stmt->execute();
        }
        
        // Log activity
        if (function_exists('logActivity')) {
            logActivity('Created family relationship', $_SESSION['user_id'], 'create', 'family_relationships', $relationship_id);
        }
        
        // Commit transaction
        $conn->commit();
        
        // Redirect with success message
        header("Location: family_relationships.php?success=added");
        exit;
        
    } catch (PDOException $e) {
        // Rollback transaction on error
        $conn->rollBack();
        
        // Log error
        error_log("Error adding relationship: " . $e->getMessage());
        
        // Redirect with error message
        header("Location: family_relationships.php?error=db_error");
        exit;
    }
}

// Function to edit a relationship
function edit_relationship() {
    global $conn;
    
    // Validate form inputs
    $required_fields = ['relationship_id', 'relationship', 'resident_id', 'related_to'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty($_POST[$field])) {
            header("Location: family_relationships.php?error=validation");
            exit;
        }
    }
    
    $relationship_id = (int)$_POST['relationship_id'];
    $relationship = htmlspecialchars(trim($_POST['relationship']));
    $resident_id = (int)$_POST['resident_id'];
    $related_to = (int)$_POST['related_to'];
    $update_reciprocal = isset($_POST['update_reciprocal']) ? true : false;
    
    try {
        // Begin transaction
        $conn->beginTransaction();
        
        // First check if relationship exists
        $check_query = "SELECT * FROM family_relationships WHERE relationship_id = :relationship_id";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bindParam(':relationship_id', $relationship_id, PDO::PARAM_INT);
        $check_stmt->execute();
        
        if ($check_stmt->rowCount() == 0) {
            // Rollback and return error
            $conn->rollBack();
            header("Location: family_relationships.php?error=not_found");
            exit;
        }
        
        $current_relationship = $check_stmt->fetch(PDO::FETCH_ASSOC);
        
        // Update primary relationship
        $update_query = "UPDATE family_relationships 
                        SET relationship = :relationship
                        WHERE relationship_id = :relationship_id";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bindParam(':relationship', $relationship, PDO::PARAM_STR);
        $update_stmt->bindParam(':relationship_id', $relationship_id, PDO::PARAM_INT);
        $update_stmt->execute();
        
        // Update reciprocal relationship if requested
        if ($update_reciprocal) {
            $reciprocal = get_reciprocal_relationship($relationship);
            
            // Find the reciprocal relationship
            $find_reciprocal_query = "SELECT * FROM family_relationships 
                                    WHERE resident_id = :related_to AND related_to = :resident_id";
            $find_reciprocal_stmt = $conn->prepare($find_reciprocal_query);
            $find_reciprocal_stmt->bindParam(':resident_id', $resident_id, PDO::PARAM_INT);
            $find_reciprocal_stmt->bindParam(':related_to', $related_to, PDO::PARAM_INT);
            $find_reciprocal_stmt->execute();
            
            if ($find_reciprocal_stmt->rowCount() > 0) {
                $reciprocal_relationship = $find_reciprocal_stmt->fetch(PDO::FETCH_ASSOC);
                
                // Update the reciprocal relationship
                $update_reciprocal_query = "UPDATE family_relationships 
                                           SET relationship = :relationship
                                           WHERE relationship_id = :relationship_id";
                $update_reciprocal_stmt = $conn->prepare($update_reciprocal_query);
                $update_reciprocal_stmt->bindParam(':relationship', $reciprocal, PDO::PARAM_STR);
                $update_reciprocal_stmt->bindParam(':relationship_id', $reciprocal_relationship['relationship_id'], PDO::PARAM_INT);
                $update_reciprocal_stmt->execute();
            }
            // If reciprocal doesn't exist but should, create it
            else if ($reciprocal) {
                $create_reciprocal_query = "INSERT INTO family_relationships (resident_id, related_to, relationship) 
                                          VALUES (:resident_id, :related_to, :relationship)";
                $create_reciprocal_stmt = $conn->prepare($create_reciprocal_query);
                $create_reciprocal_stmt->bindParam(':resident_id', $related_to, PDO::PARAM_INT);
                $create_reciprocal_stmt->bindParam(':related_to', $resident_id, PDO::PARAM_INT);
                $create_reciprocal_stmt->bindParam(':relationship', $reciprocal, PDO::PARAM_STR);
                $create_reciprocal_stmt->execute();
            }
        }
        
        // Log activity
        if (function_exists('logActivity')) {
            logActivity('Updated family relationship', $_SESSION['user_id'], 'update', 'family_relationships', $relationship_id);
        }
        
        // Commit transaction
        $conn->commit();
        
        // Redirect with success message
        header("Location: family_relationships.php?success=updated");
        exit;
        
    } catch (PDOException $e) {
        // Rollback transaction on error
        $conn->rollBack();
        
        // Log error
        error_log("Error updating relationship: " . $e->getMessage());
        
        // Redirect with error message
        header("Location: family_relationships.php?error=db_error");
        exit;
    }
}

// Function to delete a relationship
function delete_relationship() {
    global $conn;
    
    // Validate relationship_id
    if (!isset($_POST['relationship_id']) || empty($_POST['relationship_id'])) {
        header("Location: family_relationships.php?error=validation");
        exit;
    }
    
    $relationship_id = (int)$_POST['relationship_id'];
    
    try {
        // Begin transaction
        $conn->beginTransaction();
        
        // Get relationship details before deletion
        $get_query = "SELECT * FROM family_relationships WHERE relationship_id = :relationship_id";
        $get_stmt = $conn->prepare($get_query);
        $get_stmt->bindParam(':relationship_id', $relationship_id, PDO::PARAM_INT);
        $get_stmt->execute();
        
        if ($get_stmt->rowCount() == 0) {
            // Relationship not found
            $conn->rollBack();
            header("Location: family_relationships.php?error=not_found");
            exit;
        }
        
        $relationship = $get_stmt->fetch(PDO::FETCH_ASSOC);
        
        // Delete primary relationship
        $delete_query = "DELETE FROM family_relationships WHERE relationship_id = :relationship_id";
        $delete_stmt = $conn->prepare($delete_query);
        $delete_stmt->bindParam(':relationship_id', $relationship_id, PDO::PARAM_INT);
        $delete_stmt->execute();
        
        // Also delete reciprocal relationship if it exists
        $delete_reciprocal_query = "DELETE FROM family_relationships 
                                   WHERE resident_id = :related_to AND related_to = :resident_id";
        $delete_reciprocal_stmt = $conn->prepare($delete_reciprocal_query);
        $delete_reciprocal_stmt->bindParam(':resident_id', $relationship['resident_id'], PDO::PARAM_INT);
        $delete_reciprocal_stmt->bindParam(':related_to', $relationship['related_to'], PDO::PARAM_INT);
        $delete_reciprocal_stmt->execute();
        
        // Log activity
        if (function_exists('logActivity')) {
            logActivity('Deleted family relationship', $_SESSION['user_id'], 'delete', 'family_relationships', $relationship_id);
        }
        
        // Commit transaction
        $conn->commit();
        
        // Redirect with success message
        header("Location: family_relationships.php?success=deleted");
        exit;
        
    } catch (PDOException $e) {
        // Rollback transaction on error
        $conn->rollBack();
        
        // Log error
        error_log("Error deleting relationship: " . $e->getMessage());
        
        // Redirect with error message
        header("Location: family_relationships.php?error=db_error");
        exit;
    }
}
?> 