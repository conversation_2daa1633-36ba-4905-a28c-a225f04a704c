<?php
// Include database connection
include '../../includes/session.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include permission functions
include_once '../../includes/functions/permission_functions.php';

// Check if user has permission to access bail module
if (!canAccessModule('bail')) {
    $_SESSION['error'] = 'You do not have permission to access this page.';
    header('Location: ../../index.php');
    exit();
}

// Set page title
$page_title = 'View Court Hearing';

// Check if a hearing ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = 'No hearing specified to view.';
    header('Location: hearings.php');
    exit();
}

$hearing_id = $_GET['id'];

// Fetch hearing details with blotter and user information
try {
    $stmt = $conn->prepare("
        SELECT ch.*, 
               be.reference_number, be.suspect_name, be.case_type, be.status as case_status,
               u1.username as recorded_by_name
        FROM court_hearings ch
        JOIN blotter_entries be ON ch.blotter_id = be.blotter_id
        LEFT JOIN users u1 ON ch.recorded_by = u1.user_id
        WHERE ch.hearing_id = :hearing_id
    ");
    $stmt->bindParam(':hearing_id', $hearing_id);
    $stmt->execute();
    $hearing = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$hearing) {
        $_SESSION['error'] = 'Hearing not found.';
        header('Location: hearings.php');
        exit();
    }

    // Get other hearings for the same case
    $stmt = $conn->prepare("
        SELECT hearing_id, hearing_date, hearing_type, status
        FROM court_hearings 
        WHERE blotter_id = :blotter_id AND hearing_id != :hearing_id
        ORDER BY hearing_date ASC
    ");
    $stmt->bindParam(':blotter_id', $hearing['blotter_id']);
    $stmt->bindParam(':hearing_id', $hearing_id);
    $stmt->execute();
    $other_hearings = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error: ' . $e->getMessage();
    header('Location: hearings.php');
    exit();
}

// Process form submission for updating attendance or status
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Attendance update
        if (isset($_POST['action']) && $_POST['action'] == 'update_attendance') {
            $has_attended = isset($_POST['has_attended']) ? 1 : 0;
            $attendance_proof = $_POST['attendance_proof'];
            $attendance_notes = $_POST['attendance_notes'];
            $next_hearing_date = !empty($_POST['next_hearing_date']) ? $_POST['next_hearing_date'] : null;
            
            $sql = "UPDATE court_hearings SET 
                    has_attended = :has_attended,
                    attendance_proof = :attendance_proof,
                    hearing_outcome = :attendance_notes,
                    next_hearing_date = :next_hearing_date
                WHERE hearing_id = :hearing_id";
            
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':hearing_id', $hearing_id);
            $stmt->bindParam(':has_attended', $has_attended);
            $stmt->bindParam(':attendance_proof', $attendance_proof);
            $stmt->bindParam(':attendance_notes', $attendance_notes);
            $stmt->bindParam(':next_hearing_date', $next_hearing_date);
            
            if ($stmt->execute()) {
                $_SESSION['success'] = 'Attendance information updated successfully.';
                header('Location: view_hearing.php?id=' . $hearing_id);
                exit();
            } else {
                $_SESSION['error'] = 'Failed to update attendance information.';
            }
        }
        
        // Status update
        if (isset($_POST['action']) && $_POST['action'] == 'update_status') {
            $status = $_POST['status'];
            $hearing_outcome = $_POST['hearing_outcome'];
            $next_hearing_date = !empty($_POST['next_hearing_date']) ? $_POST['next_hearing_date'] : null;
            
            $sql = "UPDATE court_hearings SET 
                    status = :status,
                    hearing_outcome = :hearing_outcome,
                    next_hearing_date = :next_hearing_date
                WHERE hearing_id = :hearing_id";
            
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':hearing_id', $hearing_id);
            $stmt->bindParam(':status', $status);
            $stmt->bindParam(':hearing_outcome', $hearing_outcome);
            $stmt->bindParam(':next_hearing_date', $next_hearing_date);
            
            if ($stmt->execute()) {
                // If completed and next_hearing_date is provided, schedule new hearing
                if ($status == 'Completed' && $next_hearing_date) {
                    $insert_sql = "INSERT INTO court_hearings (
                            blotter_id, hearing_date, hearing_type, hearing_location, 
                            attendance_required, remarks, recorded_by, recorded_at
                        ) VALUES (
                            :blotter_id, :hearing_date, :hearing_type, :hearing_location, 
                            :attendance_required, :remarks, :recorded_by, NOW()
                        )";
                    
                    $insert_stmt = $conn->prepare($insert_sql);
                    $insert_stmt->bindParam(':blotter_id', $hearing['blotter_id']);
                    $insert_stmt->bindParam(':hearing_date', $next_hearing_date);
                    $insert_stmt->bindValue(':hearing_type', 'Follow-up Hearing');
                    $insert_stmt->bindParam(':hearing_location', $hearing['hearing_location']);
                    $insert_stmt->bindValue(':attendance_required', 1);
                    $insert_stmt->bindValue(':remarks', 'Follow-up hearing automatically scheduled after previous hearing');
                    $insert_stmt->bindParam(':recorded_by', $_SESSION['user_id']);
                    
                    if ($insert_stmt->execute()) {
                        $_SESSION['success'] = 'Hearing status updated successfully and new hearing scheduled.';
                    } else {
                        $_SESSION['success'] = 'Hearing status updated successfully but failed to schedule new hearing.';
                    }
                } else {
                    $_SESSION['success'] = 'Hearing status updated successfully.';
                }
                
                header('Location: view_hearing.php?id=' . $hearing_id);
                exit();
            } else {
                $_SESSION['error'] = 'Failed to update hearing status.';
            }
        }
        
    } catch (PDOException $e) {
        $_SESSION['error'] = 'Database error: ' . $e->getMessage();
    }
}

// Include header and sidebar
include '../../includes/header.php';
include '../../includes/sidebar.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="mt-4">View Court Hearing</h1>
            <ol class="breadcrumb mb-4">
                <li class="breadcrumb-item"><a href="../../index.php">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="index.php">Bail Management</a></li>
                <li class="breadcrumb-item"><a href="hearings.php">Court Hearings</a></li>
                <li class="breadcrumb-item active">View Hearing</li>
            </ol>

            <div class="row">
                <!-- Hearing Information Card -->
                <div class="col-lg-8">
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-gavel me-1"></i>
                                📅 Hearing Details
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <h5 class="text-muted">📋 Hearing Information</h5>
                                    <ul class="list-group">
                                        <li class="list-group-item">
                                            <strong>Date & Time:</strong><br>
                                            🕒 <?php echo date('F d, Y h:i A', strtotime($hearing['hearing_date'])); ?>
                                        </li>
                                        <li class="list-group-item">
                                            <strong>Hearing Type:</strong><br>
                                            🏛️ <?php echo htmlspecialchars($hearing['hearing_type']); ?>
                                        </li>
                                        <li class="list-group-item">
                                            <strong>Location:</strong><br>
                                            📍 <?php echo htmlspecialchars($hearing['hearing_location']); ?>
                                        </li>
                                        <li class="list-group-item">
                                            <strong>Status:</strong><br>
                                            <?php 
                                            $status_class = '';
                                            switch ($hearing['status']) {
                                                case 'Scheduled':
                                                    $status_class = 'bg-success';
                                                    break;
                                                case 'Completed':
                                                    $status_class = 'bg-success';
                                                    break;
                                                case 'Postponed':
                                                    $status_class = 'bg-warning';
                                                    break;
                                                case 'Cancelled':
                                                    $status_class = 'bg-danger';
                                                    break;
                                                default:
                                                    $status_class = 'bg-secondary';
                                            }
                                            ?>
                                            <span class="badge <?php echo $status_class; ?>"><?php echo htmlspecialchars($hearing['status']); ?></span>
                                        </li>
                                    </ul>
                                </div>
                                <div class="col-md-4">
                                    <h5 class="text-muted">👤 Attendance Details</h5>
                                    <ul class="list-group">
                                        <li class="list-group-item">
                                            <strong>Attendance Required:</strong><br>
                                            <?php echo $hearing['attendance_required'] ? '✅ Yes' : '❌ No'; ?>
                                        </li>
                                        <li class="list-group-item">
                                            <strong>Has Attended:</strong><br>
                                            <?php if ($hearing['attendance_required']): ?>
                                                <?php if (isset($hearing['has_attended']) && $hearing['has_attended']): ?>
                                                    <span class="badge bg-success">✅ Yes</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">❌ No</span>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">⚠️ Not Required</span>
                                            <?php endif; ?>
                                        </li>
                                        <li class="list-group-item">
                                            <strong>Attendance Proof:</strong><br>
                                            📄 <?php echo !empty($hearing['attendance_proof']) ? htmlspecialchars($hearing['attendance_proof']) : 'None'; ?>
                                        </li>
                                        <li class="list-group-item">
                                            <strong>Next Hearing Date:</strong><br>
                                            📆 <?php echo !empty($hearing['next_hearing_date']) ? date('F d, Y h:i A', strtotime($hearing['next_hearing_date'])) : 'Not set'; ?>
                                        </li>
                                    </ul>
                                </div>
                                <div class="col-md-4">
                                    <h5 class="text-muted">⚖️ Case Information</h5>
                                    <ul class="list-group">
                                        <li class="list-group-item">
                                            <strong>Reference #:</strong><br>
                                            🔢 <?php echo htmlspecialchars($hearing['reference_number']); ?>
                                        </li>
                                        <li class="list-group-item">
                                            <strong>Suspect Name:</strong><br>
                                            👤 <?php echo htmlspecialchars($hearing['suspect_name']); ?>
                                        </li>
                                        <li class="list-group-item">
                                            <strong>Case Type:</strong><br>
                                            📂 <?php echo htmlspecialchars($hearing['case_type']); ?>
                                        </li>
                                        <li class="list-group-item">
                                            <strong>Case Status:</strong><br>
                                            <?php 
                                            $case_status_class = '';
                                            switch ($hearing['case_status']) {
                                                case 'Active':
                                                    $case_status_class = 'bg-success';
                                                    break;
                                                case 'On Bail':
                                                    $case_status_class = 'bg-success';
                                                    break;
                                                case 'Closed':
                                                    $case_status_class = 'bg-secondary';
                                                    break;
                                                case 'Transferred':
                                                    $case_status_class = 'bg-info';
                                                    break;
                                                case 'Dismissed':
                                                    $case_status_class = 'bg-danger';
                                                    break;
                                                default:
                                                    $case_status_class = 'bg-secondary';
                                            }
                                            ?>
                                            <span class="badge <?php echo $case_status_class; ?>"><?php echo htmlspecialchars($hearing['case_status']); ?></span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-12">
                                    <h5 class="text-muted">📝 Remarks/Details</h5>
                                    <div class="card">
                                        <div class="card-body bg-light">
                                            <?php echo !empty($hearing['remarks']) ? nl2br(htmlspecialchars($hearing['remarks'])) : 'No remarks provided.'; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <?php if (!empty($hearing['hearing_outcome'])): ?>
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <h5 class="text-muted">📋 Hearing Outcome</h5>
                                    <div class="card">
                                        <div class="card-body bg-light">
                                            <?php echo nl2br(htmlspecialchars($hearing['hearing_outcome'])); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <h5 class="text-muted">ℹ️ Record Information</h5>
                                    <ul class="list-group">
                                        <li class="list-group-item">
                                            <strong>Created By:</strong> 👤 <?php echo htmlspecialchars($hearing['recorded_by_name']); ?>
                                        </li>
                                        <li class="list-group-item">
                                            <strong>Created At:</strong> 🕒 <?php echo date('F d, Y h:i:s A', strtotime($hearing['recorded_at'])); ?>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="mt-4 d-flex justify-content-between">
                                <div>
                                    <a href="hearings.php?blotter_id=<?php echo $hearing['blotter_id']; ?>" class="btn btn-primary">
                                        📋 View All Hearings
                                    </a>
                                    <a href="view_case.php?id=<?php echo $hearing['blotter_id']; ?>" class="btn btn-info">
                                        👁️ View Case Details
                                    </a>
                                </div>
                                
                                <?php if ($hearing['status'] == 'Scheduled'): ?>
                                <div>
                                    <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#updateStatusModal">
                                        📝 Update Status
                                    </button>
                                    <?php if ($hearing['attendance_required']): ?>
                                    <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#updateAttendanceModal">
                                        ✅ Update Attendance
                                    </button>
                                    <?php endif; ?>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Sidebar: Related Hearings -->
                <div class="col-lg-4">
                    <div class="card mb-4">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-calendar-alt me-1"></i>
                                📅 Other Hearings for This Case
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($other_hearings)): ?>
                                <div class="alert alert-info">
                                    ℹ️ No other hearings found for this case.
                                </div>
                            <?php else: ?>
                                <div class="list-group">
                                    <?php foreach ($other_hearings as $other): ?>
                                        <a href="view_hearing.php?id=<?php echo $other['hearing_id']; ?>" class="list-group-item list-group-item-action">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1"><?php echo htmlspecialchars($other['hearing_type']); ?></h6>
                                                <?php 
                                                $other_status_class = '';
                                                switch ($other['status']) {
                                                    case 'Scheduled':
                                                        $other_status_class = 'bg-success';
                                                        break;
                                                    case 'Completed':
                                                        $other_status_class = 'bg-success';
                                                        break;
                                                    case 'Postponed':
                                                        $other_status_class = 'bg-warning';
                                                        break;
                                                    case 'Cancelled':
                                                        $other_status_class = 'bg-danger';
                                                        break;
                                                    default:
                                                        $other_status_class = 'bg-secondary';
                                                }
                                                ?>
                                                <span class="badge <?php echo $other_status_class; ?>"><?php echo $other['status']; ?></span>
                                            </div>
                                            <p class="mb-1"><?php echo date('F d, Y h:i A', strtotime($other['hearing_date'])); ?></p>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                            
                            <div class="mt-3">
                                <a href="schedule_hearing.php?id=<?php echo $hearing['blotter_id']; ?>" class="btn btn-success w-100">
                                    ➕ Schedule New Hearing
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions Card -->
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-bolt me-1"></i>
                                ⚡ Quick Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="list-group">
                                <a href="view_case.php?id=<?php echo $hearing['blotter_id']; ?>" class="list-group-item list-group-item-action">
                                    <i class="fas fa-eye me-2"></i> View Full Case Details
                                </a>
                                <a href="hearings.php?blotter_id=<?php echo $hearing['blotter_id']; ?>" class="list-group-item list-group-item-action">
                                    <i class="fas fa-list me-2"></i> All Hearings for This Case
                                </a>
                                <?php if ($hearing['status'] == 'Scheduled'): ?>
                                <a href="#" class="list-group-item list-group-item-action" data-bs-toggle="modal" data-bs-target="#updateStatusModal">
                                    <i class="fas fa-edit me-2"></i> Update Hearing Status
                                </a>
                                <?php endif; ?>
                                <?php if ($hearing['status'] == 'Completed' && empty($hearing['next_hearing_date'])): ?>
                                <a href="schedule_hearing.php?id=<?php echo $hearing['blotter_id']; ?>" class="list-group-item list-group-item-action">
                                    <i class="fas fa-calendar-plus me-2"></i> Schedule Next Hearing
                                </a>
                                <?php endif; ?>
                                <?php if ($hearing['attendance_required'] && $hearing['status'] == 'Scheduled'): ?>
                                <a href="#" class="list-group-item list-group-item-action" data-bs-toggle="modal" data-bs-target="#reportViolationModal">
                                    <i class="fas fa-exclamation-triangle me-2"></i> Report Attendance Violation
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Update Attendance Modal -->
<div class="modal fade" id="updateAttendanceModal" tabindex="-1" aria-labelledby="updateAttendanceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="updateAttendanceModalLabel">✅ Update Attendance Information</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="view_hearing.php?id=<?php echo $hearing_id; ?>" method="post">
                <input type="hidden" name="action" value="update_attendance">
                <div class="modal-body">
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="has_attended" name="has_attended" <?php echo (isset($hearing['has_attended']) && $hearing['has_attended']) ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="has_attended">Has Attended</label>
                    </div>
                    <div class="mb-3">
                        <label for="attendance_proof" class="form-label">Attendance Proof/Reference</label>
                        <input type="text" class="form-control" id="attendance_proof" name="attendance_proof" value="<?php echo htmlspecialchars($hearing['attendance_proof'] ?? ''); ?>">
                        <div class="form-text">Enter any reference number, document ID, or other proof of attendance</div>
                    </div>
                    <div class="mb-3">
                        <label for="attendance_notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="attendance_notes" name="attendance_notes" rows="3"><?php echo htmlspecialchars($hearing['hearing_outcome'] ?? ''); ?></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="next_hearing_date" class="form-label">Next Hearing Date (Optional)</label>
                        <input type="datetime-local" class="form-control" id="next_hearing_date" name="next_hearing_date" value="<?php echo !empty($hearing['next_hearing_date']) ? date('Y-m-d\TH:i', strtotime($hearing['next_hearing_date'])) : ''; ?>">
                        <div class="form-text">If set, this will update the next scheduled hearing date</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1" aria-labelledby="updateStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="updateStatusModalLabel">📝 Update Hearing Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="view_hearing.php?id=<?php echo $hearing_id; ?>" method="post">
                <input type="hidden" name="action" value="update_status">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="Scheduled" <?php echo $hearing['status'] == 'Scheduled' ? 'selected' : ''; ?>>Scheduled</option>
                            <option value="Completed" <?php echo $hearing['status'] == 'Completed' ? 'selected' : ''; ?>>Completed</option>
                            <option value="Postponed" <?php echo $hearing['status'] == 'Postponed' ? 'selected' : ''; ?>>Postponed</option>
                            <option value="Cancelled" <?php echo $hearing['status'] == 'Cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="hearing_outcome" class="form-label">Hearing Outcome/Notes</label>
                        <textarea class="form-control" id="hearing_outcome" name="hearing_outcome" rows="3" required><?php echo htmlspecialchars($hearing['hearing_outcome'] ?? ''); ?></textarea>
                    </div>
                    <div class="mb-3" id="nextHearingContainer">
                        <label for="next_hearing_date" class="form-label">Next Hearing Date (Optional)</label>
                        <input type="datetime-local" class="form-control" id="next_hearing_date" name="next_hearing_date" value="<?php echo !empty($hearing['next_hearing_date']) ? date('Y-m-d\TH:i', strtotime($hearing['next_hearing_date'])) : ''; ?>">
                        <div class="form-text">If set, a new hearing will automatically be scheduled</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Report Violation Modal -->
<div class="modal fade" id="reportViolationModal" tabindex="-1" aria-labelledby="reportViolationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="reportViolationModalLabel">⚠️ Report Attendance Violation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>To report a bail violation for missed hearing, please update the hearing status first, then visit the Bail Violations page.</p>
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal" data-bs-toggle="modal" data-bs-target="#updateStatusModal">
                        Update Hearing Status First
                    </button>
                    <a href="bail_violations.php?blotter_id=<?php echo $hearing['blotter_id']; ?>" class="btn btn-danger">
                        Go to Bail Violations
                    </a>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<?php include '../../includes/footer.php'; ?>

<!-- Toast container -->
<div class="toast-container position-fixed top-0 end-0 p-3">
    <div id="successToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex align-items-center p-3">
            <div class="me-2">✅</div>
            <div class="me-auto"><?php echo isset($_SESSION['success']) ? $_SESSION['success'] : (isset($_SESSION['error']) ? $_SESSION['error'] : ''); ?></div>
            <div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Show/hide next hearing date based on status
        $('#status').change(function() {
            if ($(this).val() === 'Completed') {
                $('#nextHearingContainer').show();
            } else {
                $('#nextHearingContainer').hide();
                $('#next_hearing_date').val('');
            }
        });
        
        // Initialize based on current selection
        if ($('#status').val() === 'Completed') {
            $('#nextHearingContainer').show();
        } else {
            $('#nextHearingContainer').hide();
        }

        // Show toast notification if there's a message
        <?php if(isset($_SESSION['success']) || isset($_SESSION['error'])): ?>
        // Update toast styling based on message type
        const toast = document.getElementById('successToast');
        
        <?php if(isset($_SESSION['success'])): ?>
        toast.style.backgroundColor = '#28a745'; // success green
        toast.style.color = 'white';
        <?php elseif(isset($_SESSION['error'])): ?>
        toast.style.backgroundColor = '#dc3545'; // danger red
        toast.style.color = 'white';
        <?php endif; ?>
        
        const successToast = new bootstrap.Toast(toast, {
            delay: 5000
        });
        successToast.show();

        <?php 
        // Clear session messages after displaying
        if(isset($_SESSION['success'])) unset($_SESSION['success']);
        if(isset($_SESSION['error'])) unset($_SESSION['error']);
        ?>
        <?php endif; ?>
    });
</script> 