<?php
// Include database connection
include '../../includes/session.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include permission functions
include_once '../../includes/functions/permission_functions.php';

// Check if user has permission to access bail module
if (!canAccessModule('bail')) {
    $_SESSION['error'] = 'You do not have permission to access the Bail Assessment page.';
    header('Location: ../../index.php');
    exit();
}

// Set page title
$page_title = 'Bail Assessment';

// Check if a blotter ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    // Instead of redirecting, show a list of cases that need assessment
    $list_mode = true;
    
    // Get cases that need assessment
    try {
        $stmt = $conn->query("
            SELECT be.*, ba.is_bailable, ba.bail_amount
            FROM blotter_entries be
            LEFT JOIN bail_assessments ba ON be.blotter_id = ba.blotter_id
            WHERE be.status = 'Active' 
            ORDER BY be.created_at DESC
        ");
        $assessment_cases = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $_SESSION['error'] = 'Database error: ' . $e->getMessage();
        $assessment_cases = [];
    }
} else {
    $list_mode = false;
    $blotter_id = $_GET['id'];
    
    // Fetch blotter entry details
    try {
        $stmt = $conn->prepare("
            SELECT be.*, ba.assessment_id, ba.is_bailable, ba.bail_amount, ba.legal_basis, ba.remarks, 
                   ba.assessment_details, ba.recommended_by as assessed_by, ba.assessment_date as assessed_at,
                   ba.approved_by, u1.username as assessor_name, u2.username as approver_name
            FROM blotter_entries be
            LEFT JOIN bail_assessments ba ON be.blotter_id = ba.blotter_id
            LEFT JOIN users u1 ON ba.recommended_by = u1.user_id
            LEFT JOIN users u2 ON ba.approved_by = u2.user_id
            WHERE be.blotter_id = :blotter_id
        ");
        $stmt->bindParam(':blotter_id', $blotter_id);
        $stmt->execute();
        $blotter = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$blotter) {
            $_SESSION['error'] = 'Blotter entry not found.';
            header('Location: blotter.php');
            exit();
        }

        // Check if status allows for assessment
        if ($blotter['status'] != 'Active' && $blotter['status'] != 'On Bail') {
            $_SESSION['error'] = 'Bail assessment is only allowed for Active or On Bail cases.';
            header('Location: blotter.php');
            exit();
        }
    } catch (PDOException $e) {
        $_SESSION['error'] = 'Database error: ' . $e->getMessage();
        header('Location: blotter.php');
        exit();
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $is_bailable = isset($_POST['is_bailable']) ? intval($_POST['is_bailable']) : 0;
        $bail_amount = ($is_bailable == 1) ? $_POST['bail_amount'] : 0;
        $legal_basis = $_POST['legal_basis'];
        $remarks = $_POST['remarks'];
        $recommended_by = $_SESSION['user_id'];
        $assessment_date = date('Y-m-d H:i:s');
        $assessment_details = $_POST['assessment_details'] ?? '';
        $approved_by = $_SESSION['user_type'] == 'Admin' || $_SESSION['user_type'] == 'Barangay Captain' ? $_SESSION['user_id'] : null;

        // Check if an assessment already exists
        if ($blotter['assessment_id']) {
            // Update existing assessment
            $sql = "UPDATE bail_assessments SET 
                    is_bailable = :is_bailable,
                    bail_amount = :bail_amount,
                    legal_basis = :legal_basis,
                    remarks = :remarks,
                    recommended_by = :recommended_by,
                    assessment_date = :assessment_date,
                    assessment_details = :assessment_details,
                    approved_by = :approved_by
                WHERE assessment_id = :assessment_id";
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':assessment_id', $blotter['assessment_id']);
        } else {
            // Create new assessment
            $sql = "INSERT INTO bail_assessments (
                    blotter_id, is_bailable, bail_amount, legal_basis, 
                    remarks, recommended_by, assessment_date, assessment_details, approved_by
                ) VALUES (
                    :blotter_id, :is_bailable, :bail_amount, :legal_basis, 
                    :remarks, :recommended_by, :assessment_date, :assessment_details, :approved_by
                )";
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':blotter_id', $blotter_id);
        }

        $stmt->bindParam(':is_bailable', $is_bailable);
        $stmt->bindParam(':bail_amount', $bail_amount);
        $stmt->bindParam(':legal_basis', $legal_basis);
        $stmt->bindParam(':remarks', $remarks);
        $stmt->bindParam(':recommended_by', $recommended_by);
        $stmt->bindParam(':assessment_date', $assessment_date);
        $stmt->bindParam(':assessment_details', $assessment_details);
        $stmt->bindParam(':approved_by', $approved_by);

        if ($stmt->execute()) {
            // If case is bailable, update blotter status
            if ($is_bailable == 1) {
                $status_update = $conn->prepare("UPDATE blotter_entries SET status = 'Active' WHERE blotter_id = :blotter_id");
                $status_update->bindParam(':blotter_id', $blotter_id);
                $status_update->execute();
            }

            $_SESSION['success'] = 'Bail assessment completed successfully.';

            // If the case is bailable and requested to proceed to payment
            if ($is_bailable == 1 && isset($_POST['proceed_to_payment']) && $_POST['proceed_to_payment'] == '1') {
                header('Location: bail_payment.php?id=' . $blotter_id);
                exit();
            } else {
                header('Location: blotter.php');
                exit();
            }
        } else {
            $_SESSION['error'] = 'Failed to save bail assessment.';
        }
    } catch (PDOException $e) {
        $_SESSION['error'] = 'Database error: ' . $e->getMessage();
    }
}

// Include header and sidebar
include '../../includes/header.php';
include '../../includes/sidebar.php';
include 'bail_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="mt-4">Bail Assessment</h1>
            <ol class="breadcrumb mb-4">
                <li class="breadcrumb-item"><a href="../../index.php">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="index.php">Bail Management</a></li>
                <?php if ($list_mode): ?>
                    <li class="breadcrumb-item active">Bail Assessment</li>
                <?php else: ?>
                    <li class="breadcrumb-item"><a href="blotter.php">Blotter Entries</a></li>
                    <li class="breadcrumb-item active">Bail Assessment</li>
                <?php endif; ?>
            </ol>

            <?php if ($list_mode): ?>
                <!-- List of Cases Pending Assessment -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            ⚖️ Cases Pending Bail Assessment
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Ref #</th>
                                        <th>Suspect</th>
                                        <th>Case Type</th>
                                        <th>Status</th>
                                        <th>Incident Date</th>
                                        <th>Assessment Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($assessment_cases)): ?>
                                        <tr>
                                            <td colspan="7" class="text-center">No cases pending assessment.</td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($assessment_cases as $case): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($case['reference_number']); ?></td>
                                                <td><?php echo htmlspecialchars($case['suspect_name']); ?></td>
                                                <td><?php echo htmlspecialchars($case['case_type']); ?></td>
                                                <td><?php echo getBadgeHtml($case['status']); ?></td>
                                                <td><?php echo date('M d, Y', strtotime($case['incident_date'])); ?></td>
                                                <td>
                                                    <?php if ($case['is_bailable'] === null): ?>
                                                        <?php echo getBadgeHtml('Pending'); ?>
                                                    <?php elseif ($case['is_bailable'] == 1): ?>
                                                        <?php echo getBadgeHtml('Bailable'); ?>
                                                    <?php else: ?>
                                                        <?php echo getBadgeHtml('Non-Bailable'); ?>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <a href="bail_assessment.php?id=<?php echo $case['blotter_id']; ?>" class="btn btn-sm btn-primary">
                                                        ⚖️ Assess
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <!-- Blotter Information -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            📋 Blotter Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <ul class="list-group">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <strong>Reference Number:</strong>
                                        <span>🔢 <?php echo htmlspecialchars($blotter['reference_number']); ?></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <strong>Suspect Name:</strong>
                                        <span>🧑 <?php echo htmlspecialchars($blotter['suspect_name']); ?></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <strong>Complainant Name:</strong>
                                        <span>👤 <?php echo htmlspecialchars($blotter['complainant_name']); ?></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <strong>Case Type:</strong>
                                        <span>📂 <?php echo htmlspecialchars($blotter['case_type']); ?></span>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-group">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <strong>Incident Date:</strong>
                                        <span>📅 <?php echo date('M d, Y h:i A', strtotime($blotter['incident_date'])); ?></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <strong>Arrest Date:</strong>
                                        <span>🚨 <?php echo date('M d, Y h:i A', strtotime($blotter['arrest_date'])); ?></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <strong>Arresting Officer:</strong>
                                        <span>👮 <?php echo htmlspecialchars($blotter['arresting_officer']); ?></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <strong>Detention Facility:</strong>
                                        <span>🏢 <?php echo htmlspecialchars($blotter['detention_facility']); ?></span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <strong>📝 Incident Details</strong>
                                    </div>
                                    <div class="card-body">
                                        <p class="card-text"><?php echo nl2br(htmlspecialchars($blotter['incident_details'])); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bail Assessment Form -->
                <div class="card mb-4">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            ⚖️ Bail Assessment Form
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if ($blotter['assessment_id']): ?>
                            <div class="alert alert-info mb-4">
                                ℹ️ This case has already been assessed by <?php echo htmlspecialchars($blotter['assessor_name']); ?> on <?php echo isset($blotter['assessed_at']) ? date('M d, Y h:i A', strtotime($blotter['assessed_at'])) : 'Unknown Date'; ?>. 
                                You are now editing the previous assessment.
                                <?php if (!empty($blotter['approved_by'])): ?>
                                <br><br>
                                ✅ <strong>Approved by:</strong> <?php echo htmlspecialchars($blotter['approver_name']); ?>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" action="bail_assessment.php?id=<?php echo $blotter_id; ?>" id="assessmentForm">
                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <strong>🧐 Assessment Decision</strong>
                                        </div>
                                        <div class="card-body">
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label class="form-label">Is this case bailable?</label>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="is_bailable" id="bailable_yes" value="1" <?php echo ($blotter['is_bailable'] == 1) ? 'checked' : ''; ?> onchange="toggleBailAmount()">
                                                        <label class="form-check-label" for="bailable_yes">
                                                            ✅ Yes, this case is eligible for bail
                                                        </label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="is_bailable" id="bailable_no" value="0" <?php echo ($blotter['is_bailable'] === 0 || $blotter['is_bailable'] === '0') ? 'checked' : ''; ?> onchange="toggleBailAmount()">
                                                        <label class="form-check-label" for="bailable_no">
                                                            🚫 No, this case is not eligible for bail
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6" id="bail_amount_container" style="<?php echo ($blotter['is_bailable'] == 1) ? '' : 'display: none;'; ?>">
                                                    <div class="form-floating mb-3">
                                                        <input type="number" class="form-control" id="bail_amount" name="bail_amount" placeholder="Bail Amount" min="0" step="0.01" value="<?php echo $blotter['bail_amount'] ?? '0.00'; ?>">
                                                        <label for="bail_amount">Bail Amount (💰 ₱)</label>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="form-floating mb-3">
                                                <textarea class="form-control" id="legal_basis" name="legal_basis" placeholder="Legal Basis" style="height: 100px" required><?php echo htmlspecialchars($blotter['legal_basis'] ?? ''); ?></textarea>
                                                <label for="legal_basis">Legal Basis for Decision</label>
                                                <div class="form-text">Cite the relevant laws, rules, or guidelines used for this assessment.</div>
                                            </div>

                                            <div class="form-floating mb-3">
                                                <textarea class="form-control" id="assessment_details" name="assessment_details" placeholder="Assessment Details" style="height: 100px"><?php echo htmlspecialchars($blotter['assessment_details'] ?? ''); ?></textarea>
                                                <label for="assessment_details">Assessment Details</label>
                                                <div class="form-text">Provide detailed explanation for the bail assessment decision.</div>
                                            </div>

                                            <div class="form-floating mb-3">
                                                <textarea class="form-control" id="remarks" name="remarks" placeholder="Remarks" style="height: 100px"><?php echo htmlspecialchars($blotter['remarks'] ?? ''); ?></textarea>
                                                <label for="remarks">Additional Remarks</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-check mb-3" id="proceed_container" style="<?php echo ($blotter['is_bailable'] == 1) ? '' : 'display: none;'; ?>">
                                <input class="form-check-input" type="checkbox" id="proceed_to_payment" name="proceed_to_payment" value="1">
                                <label class="form-check-label" for="proceed_to_payment">
                                    💸 Proceed directly to bail payment after saving
                                </label>
                            </div>
                            
                            <div class="text-end">
                                <a href="blotter.php" class="btn btn-secondary me-2">❌ Cancel</a>
                                <button type="submit" class="btn btn-primary">💾 Save Assessment</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Legal Reference Guide -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            📚 Legal Reference Guide
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="accordion" id="legalReferenceAccordion">
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="bailableOffensesHeading">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#bailableOffensesCollapse" aria-expanded="false" aria-controls="bailableOffensesCollapse">
                                        ✅ Bailable Offenses
                                    </button>
                                </h2>
                                <div id="bailableOffensesCollapse" class="accordion-collapse collapse" aria-labelledby="bailableOffensesHeading" data-bs-parent="#legalReferenceAccordion">
                                    <div class="accordion-body">
                                        <p>Under Philippine law, the following offenses are generally considered bailable:</p>
                                        <ul>
                                            <li>Light felonies (delitos menos graves) - imprisonment of not more than 30 days</li>
                                            <li>Less grave felonies (delitos menos graves) - imprisonment of 1 month and 1 day to 6 years</li>
                                            <li>Grave felonies (delitos graves) with certain exceptions - imprisonment of 6 years and 1 day to 20 years</li>
                                        </ul>
                                        <p>Recommended bail amounts for common offenses:</p>
                                        <ul>
                                            <li>Simple theft: ₱10,000 - ₱20,000</li>
                                            <li>Physical injuries (slight): ₱2,000 - ₱6,000</li>
                                            <li>Physical injuries (less serious): ₱6,000 - ₱12,000</li>
                                            <li>Estafa (small amount): ₱10,000 - ₱20,000</li>
                                            <li>Slight oral defamation: ₱2,000 - ₱6,000</li>
                                            <li>Simple slander: ₱2,000 - ₱10,000</li>
                                        </ul>
                                        <p><strong>Legal Basis:</strong> Rule 114 of the Rules of Court, Sections 4-6</p>
                                    </div>
                                </div>
                            </div>
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="nonBailableOffensesHeading">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#nonBailableOffensesCollapse" aria-expanded="false" aria-controls="nonBailableOffensesCollapse">
                                        🚫 Non-Bailable Offenses
                                    </button>
                                </h2>
                                <div id="nonBailableOffensesCollapse" class="accordion-collapse collapse" aria-labelledby="nonBailableOffensesHeading" data-bs-parent="#legalReferenceAccordion">
                                    <div class="accordion-body">
                                        <p>Under Philippine law, the following offenses are generally considered non-bailable when the evidence of guilt is strong:</p>
                                        <ul>
                                            <li>Capital offenses (formerly punishable by death) such as:</li>
                                            <ul>
                                                <li>Qualified piracy</li>
                                                <li>Qualified bribery</li>
                                                <li>Destructive arson</li>
                                                <li>Rape with homicide</li>
                                                <li>Murder</li>
                                                <li>Treason during wartime</li>
                                                <li>Kidnapping for ransom</li>
                                                <li>Plunder</li>
                                                <li>Drug-related offenses with certain qualifying circumstances</li>
                                            </ul>
                                        </ul>
                                        <p><strong>Legal Basis:</strong> Article III, Section 13 of the 1987 Philippine Constitution; Rule 114, Section 7 of the Rules of Court</p>
                                    </div>
                                </div>
                            </div>
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="bailProcedureHeading">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#bailProcedureCollapse" aria-expanded="false" aria-controls="bailProcedureCollapse">
                                        📋 Bail Procedure Guidelines
                                    </button>
                                </h2>
                                <div id="bailProcedureCollapse" class="accordion-collapse collapse" aria-labelledby="bailProcedureHeading" data-bs-parent="#legalReferenceAccordion">
                                    <div class="accordion-body">
                                        <p>When determining bail eligibility and amount, consider the following factors:</p>
                                        <ol>
                                            <li>Nature and circumstances of the offense</li>
                                            <li>Penalty for the offense</li>
                                            <li>Character and reputation of the accused</li>
                                            <li>Age and health of the accused</li>
                                            <li>Weight of the evidence against the accused</li>
                                            <li>Probability of the accused appearing at trial</li>
                                            <li>Flight risk and community ties</li>
                                            <li>Source of bail funds</li>
                                            <li>Previous criminal record</li>
                                            <li>Threat or danger to the community</li>
                                        </ol>
                                        <p><strong>Legal Basis:</strong> Rule 114, Section 9 of the Rules of Court; Supreme Court Administrative Circular No. 12-94</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- JavaScript for toggling bail amount field -->
            <script>
                function toggleBailAmount() {
                    const isBailable = document.getElementById('bailable_yes').checked;
                    document.getElementById('bail_amount_container').style.display = isBailable ? 'block' : 'none';
                    document.getElementById('proceed_container').style.display = isBailable ? 'block' : 'none';
                    
                    if (!isBailable) {
                        document.getElementById('bail_amount').value = '0.00';
                    }
                }
            </script>
        </main>
    </div>
</div>

<?php include '../../includes/footer.php'; ?>

<!-- Toast container -->
<div class="toast-container position-fixed top-0 end-0 p-3">
    <div id="successToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex align-items-center p-3">
            <div class="me-2">✅</div>
            <div class="me-auto"><?php echo isset($_SESSION['success']) ? $_SESSION['success'] : (isset($_SESSION['error']) ? $_SESSION['error'] : ''); ?></div>
            <div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    </div>
</div>

<script>
    // Any existing scripts...

    // Show toast notification if there's a message
    document.addEventListener('DOMContentLoaded', function() {
        <?php if(isset($_SESSION['success']) || isset($_SESSION['error'])): ?>
        // Update toast styling based on message type
        const toast = document.getElementById('successToast');
        
        <?php if(isset($_SESSION['success'])): ?>
        toast.style.backgroundColor = '#28a745'; // success green
        toast.style.color = 'white';
        <?php elseif(isset($_SESSION['error'])): ?>
        toast.style.backgroundColor = '#dc3545'; // danger red
        toast.style.color = 'white';
        <?php endif; ?>
        
        const successToast = new bootstrap.Toast(toast, {
            delay: 5000
        });
        successToast.show();

        <?php 
        // Clear session messages after displaying
        if(isset($_SESSION['success'])) unset($_SESSION['success']);
        if(isset($_SESSION['error'])) unset($_SESSION['error']);
        ?>
        <?php endif; ?>
    });
</script> 