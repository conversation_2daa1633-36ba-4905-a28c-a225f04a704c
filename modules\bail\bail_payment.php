<?php
// Function to generate unique receipt number
function generateReceiptNumber($conn) {
    $date_prefix = "RCPT-" . date('Ymd');
    
    // Get the latest receipt number with the same date prefix
    $stmt = $conn->prepare("
        SELECT receipt_number FROM bail_payments 
        WHERE receipt_number LIKE :prefix 
        ORDER BY payment_id DESC LIMIT 1
    ");
    $search_prefix = $date_prefix . "-%";
    $stmt->bindParam(':prefix', $search_prefix);
    $stmt->execute();
    
    $last_number = 0;
    if ($row = $stmt->fetch()) {
        // Extract the numerical part
        $parts = explode('-', $row['receipt_number']);
        if (count($parts) >= 3) {
            $last_number = intval($parts[2]);
        }
    }
    
    // Increment and pad to 4 digits
    $next_number = str_pad($last_number + 1, 4, '0', STR_PAD_LEFT);
    
    return $date_prefix . "-" . $next_number;
}

// Include database connection
include '../../includes/session.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include permission functions
include '../../includes/functions/permission_functions.php';

// Check if user has permission to access bail module
if (!canAccessModule('bail')) {
    $_SESSION['error'] = 'You do not have permission to access the Bail Payment page.';
    header('Location: ../../index.php');
    exit();
}

// Set page title
$page_title = 'Bail Payment';

// Check if a blotter ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    // Instead of redirecting, show a list of cases eligible for bail payment
    $list_mode = true;
    
    // Get cases that are bailable and require payment
    try {
        $stmt = $conn->query("
            SELECT be.*, ba.is_bailable, ba.bail_amount,
                   (SELECT SUM(amount) FROM bail_payments WHERE blotter_id = be.blotter_id AND payment_status = 'Completed') as total_paid
            FROM blotter_entries be
            JOIN bail_assessments ba ON be.blotter_id = ba.blotter_id
            WHERE be.status = 'Active' AND ba.is_bailable = 1
            ORDER BY be.created_at DESC
        ");
        $payment_cases = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $_SESSION['error'] = 'Database error: ' . $e->getMessage();
        $payment_cases = [];
    }
} else {
    $list_mode = false;
    $blotter_id = $_GET['id'];

    // Fetch blotter entry and bail assessment details
    try {
        $stmt = $conn->prepare("
            SELECT be.*, ba.assessment_id, ba.is_bailable, ba.bail_amount, ba.legal_basis,
                   (SELECT SUM(amount) FROM bail_payments WHERE blotter_id = be.blotter_id AND payment_status = 'Completed') as total_paid
            FROM blotter_entries be
            LEFT JOIN bail_assessments ba ON be.blotter_id = ba.blotter_id
            WHERE be.blotter_id = :blotter_id
        ");
        $stmt->bindParam(':blotter_id', $blotter_id);
        $stmt->execute();
        $blotter = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$blotter) {
            $_SESSION['error'] = 'Blotter entry not found.';
            header('Location: blotter.php');
            exit();
        }

        // Check if case is bailable
        if (!$blotter['is_bailable']) {
            $_SESSION['error'] = 'This case is not eligible for bail.';
            header('Location: view_case.php?id=' . $blotter_id);
            exit();
        }

        // Get previous payments
        $stmt = $conn->prepare("
            SELECT bp.*, u.username as processed_by_name
            FROM bail_payments bp
            LEFT JOIN users u ON bp.received_by = u.user_id
            WHERE bp.blotter_id = :blotter_id
            ORDER BY bp.payment_date DESC
        ");
        $stmt->bindParam(':blotter_id', $blotter_id);
        $stmt->execute();
        $previous_payments = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Calculate remaining balance
        $total_paid = $blotter['total_paid'] ?: 0;
        $remaining_balance = $blotter['bail_amount'] - $total_paid;

    } catch (PDOException $e) {
        $_SESSION['error'] = 'Database error: ' . $e->getMessage();
        header('Location: blotter.php');
        exit();
    }
}

// Process payment form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_payment'])) {
    try {
        $payment_method = $_POST['payment_method'];
        $amount = $_POST['amount'];
        $reference_number = $_POST['reference_number'];
        $receipt_number = generateReceiptNumber($conn);
        $payor_name = $_POST['payor_name'];
        $payor_contact = $_POST['payor_contact'];
        $payor_address = $_POST['payor_address'];
        $payor_relationship = $_POST['payor_relationship'];
        $remarks = $_POST['remarks'];
        $payment_status = 'Completed';
        $payment_date = date('Y-m-d H:i:s');
        $received_by = $_SESSION['user_id'];

        // Validate payment amount
        if ($amount <= 0 || $amount > $remaining_balance) {
            $_SESSION['error'] = 'Invalid payment amount.';
        } else {
            // Insert payment record
            $sql = "INSERT INTO bail_payments (
                    blotter_id, payment_method, amount, reference_number, receipt_number,
                    payer_name, payer_contact, payer_relation, 
                    remarks, payment_status, payment_date, received_by
                ) VALUES (
                    :blotter_id, :payment_method, :amount, :reference_number, :receipt_number,
                    :payer_name, :payer_contact, :payer_relation, 
                    :remarks, :payment_status, :payment_date, :received_by
                )";
            
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':blotter_id', $blotter_id);
            $stmt->bindParam(':payment_method', $payment_method);
            $stmt->bindParam(':amount', $amount);
            $stmt->bindParam(':reference_number', $reference_number);
            $stmt->bindParam(':receipt_number', $receipt_number);
            $stmt->bindParam(':payer_name', $payor_name);
            $stmt->bindParam(':payer_contact', $payor_contact);
            $stmt->bindParam(':payer_relation', $payor_relationship);
            $stmt->bindParam(':remarks', $remarks);
            $stmt->bindParam(':payment_status', $payment_status);
            $stmt->bindParam(':payment_date', $payment_date);
            $stmt->bindParam(':received_by', $received_by);
            
            if ($stmt->execute()) {
                // Check if full payment is made
                $new_total_paid = $total_paid + $amount;
                
                if ($new_total_paid >= $blotter['bail_amount']) {
                    // Update blotter status to On Bail if paid in full
                    $status_update = $conn->prepare("UPDATE blotter_entries SET status = 'On Bail' WHERE blotter_id = :blotter_id");
                    $status_update->bindParam(':blotter_id', $blotter_id);
                    $status_update->execute();
                    
                    $_SESSION['success'] = 'Payment recorded successfully. Bail has been fully paid. Please proceed to release order processing.';
                    header('Location: release_order.php?id=' . $blotter_id);
                    exit();
                } else {
                    $_SESSION['success'] = 'Payment recorded successfully. Remaining balance: ₱' . number_format($blotter['bail_amount'] - $new_total_paid, 2);
                    header('Location: bail_payment.php?id=' . $blotter_id);
                    exit();
                }
            } else {
                $_SESSION['error'] = 'Failed to record payment.';
            }
        }
    } catch (PDOException $e) {
        $_SESSION['error'] = 'Database error: ' . $e->getMessage();
    }
}

// Include header and sidebar
include '../../includes/header.php';
include '../../includes/sidebar.php';
include 'bail_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="mt-4">Bail Payment Processing</h1>
            <ol class="breadcrumb mb-4">
                <li class="breadcrumb-item"><a href="../../index.php">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="index.php">Bail Management</a></li>
                <?php if ($list_mode): ?>
                    <li class="breadcrumb-item active">Bail Payment Processing</li>
                <?php else: ?>
                    <li class="breadcrumb-item"><a href="blotter.php">Blotter Entries</a></li>
                    <li class="breadcrumb-item active">Bail Payment</li>
                <?php endif; ?>
            </ol>

            <?php if ($list_mode): ?>
                <!-- List of Cases Eligible for Bail Payment -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            💰 Cases Eligible for Bail Payment
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Ref #</th>
                                        <th>Suspect</th>
                                        <th>Case Type</th>
                                        <th>Status</th>
                                        <th>Bail Amount</th>
                                        <th>Paid Amount</th>
                                        <th>Remaining</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($payment_cases)): ?>
                                        <tr>
                                            <td colspan="8" class="text-center">No cases eligible for bail payment found.</td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($payment_cases as $case): ?>
                                            <?php 
                                                $total_paid = $case['total_paid'] ?: 0;
                                                $remaining = $case['bail_amount'] - $total_paid;
                                            ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($case['reference_number']); ?></td>
                                                <td><?php echo htmlspecialchars($case['suspect_name']); ?></td>
                                                <td><?php echo htmlspecialchars($case['case_type']); ?></td>
                                                <td><?php echo getBadgeHtml($case['status']); ?></td>
                                                <td><?php echo formatCurrency($case['bail_amount']); ?></td>
                                                <td><?php echo formatCurrency($total_paid); ?></td>
                                                <td><?php echo formatCurrency($remaining); ?></td>
                                                <td>
                                                    <a href="bail_payment.php?id=<?php echo $case['blotter_id']; ?>" class="btn btn-sm btn-primary">
                                                        💸 Process Payment
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <!-- Case and Payment Summary -->
                <div class="row mb-4">
                    <div class="col-xl-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    📋 Case Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-group mb-3">
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Reference Number:</span>
                                        <strong>🔢 <?php echo htmlspecialchars($blotter['reference_number']); ?></strong>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Suspect Name:</span>
                                        <strong>🧑 <?php echo htmlspecialchars($blotter['suspect_name']); ?></strong>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Case Type:</span>
                                        <strong>📂 <?php echo htmlspecialchars($blotter['case_type']); ?></strong>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Status:</span>
                                        <strong>
                                            <?php 
                                            $status_class = '';
                                            switch ($blotter['status']) {
                                                case 'Active':
                                                    $status_class = 'bg-success';
                                                    break;
                                                case 'On Bail':
                                                    $status_class = 'bg-success';
                                                    break;
                                                default:
                                                    $status_class = 'bg-secondary';
                                            }
                                            ?>
                                            <span class="badge <?php echo $status_class; ?>"><?php echo htmlspecialchars($blotter['status']); ?></span>
                                        </strong>
                                    </li>
                                </ul>
                                <div class="text-center">
                                    <a href="view_case.php?id=<?php echo $blotter_id; ?>" class="btn btn-info btn-sm">
                                        👁️ View Full Case Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-xl-8">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">
                                    💰 Bail Payment Summary
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="card bg-light mb-3">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Total Bail Amount</h6>
                                                <h2 class="text-primary">💰 ₱<?php echo number_format($blotter['bail_amount'], 2); ?></h2>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card bg-light mb-3">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Amount Paid</h6>
                                                <h2 class="text-success">💵 ₱<?php echo number_format($total_paid, 2); ?></h2>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card bg-light mb-3">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Remaining Balance</h6>
                                                <h2 class="<?php echo ($remaining_balance > 0) ? 'text-danger' : 'text-success'; ?>">
                                                    <?php echo ($remaining_balance > 0) ? '⚠️' : '✅'; ?> ₱<?php echo number_format($remaining_balance, 2); ?>
                                                </h2>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="progress mb-3" style="height: 20px;">
                                    <?php 
                                    $percent_paid = ($blotter['bail_amount'] > 0) ? ($total_paid / $blotter['bail_amount']) * 100 : 0;
                                    ?>
                                    <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $percent_paid; ?>%;" 
                                         aria-valuenow="<?php echo $percent_paid; ?>" aria-valuemin="0" aria-valuemax="100">
                                        <?php echo round($percent_paid); ?>%
                                    </div>
                                </div>
                                
                                <?php if ($remaining_balance <= 0): ?>
                                    <div class="alert alert-success text-center" role="alert">
                                        ✅ Bail has been fully paid. Proceed to release order processing.
                                        <div class="mt-2">
                                            <a href="release_order.php?id=<?php echo $blotter_id; ?>" class="btn btn-primary btn-sm">
                                                📄 Process Release Order
                                            </a>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Form and History -->
                <div class="row">
                    <?php if ($remaining_balance > 0): ?>
                        <div class="col-xl-6">
                            <div class="card mb-4">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">
                                        💾 Record New Payment
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <form method="POST" action="bail_payment.php?id=<?php echo $blotter_id; ?>" id="paymentForm">
                                        <div class="form-floating mb-3">
                                            <select class="form-select" id="payment_method" name="payment_method" required>
                                                <option value="">Select payment method</option>
                                                <option value="Cash">Cash</option>
                                                <option value="Check">Check</option>
                                                <option value="Bank Transfer">Bank Transfer</option>
                                                <option value="Online Payment">Online Payment</option>
                                            </select>
                                            <label for="payment_method">Payment Method</label>
                                        </div>
                                        
                                        <div class="form-floating mb-3">
                                            <input type="number" class="form-control" id="amount" name="amount" placeholder="Amount" min="1" max="<?php echo $remaining_balance; ?>" step="0.01" required>
                                            <label for="amount">Amount (₱)</label>
                                            <div class="form-text">Maximum amount: ₱<?php echo number_format($remaining_balance, 2); ?></div>
                                        </div>
                                        
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="reference_number" name="reference_number" placeholder="Reference Number">
                                            <label for="reference_number">Reference Number</label>
                                        </div>
                                        
                                        <div class="alert alert-info mb-3">
                                            ℹ️ Receipt numbers will be automatically generated when the payment is recorded.
                                        </div>
                                        
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="payor_name" name="payor_name" placeholder="Payor Name" required>
                                            <label for="payor_name">Payor Name</label>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-floating mb-3">
                                                    <input type="text" class="form-control" id="payor_contact" name="payor_contact" placeholder="Contact Number">
                                                    <label for="payor_contact">Contact Number</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-floating mb-3">
                                                    <select class="form-select" id="payor_relationship" name="payor_relationship" required>
                                                        <option value="">Select relationship</option>
                                                        <option value="Self">Self</option>
                                                        <option value="Family Member">Family Member</option>
                                                        <option value="Spouse">Spouse</option>
                                                        <option value="Parent">Parent</option>
                                                        <option value="Child">Child</option>
                                                        <option value="Sibling">Sibling</option>
                                                        <option value="Legal Counsel">Legal Counsel</option>
                                                        <option value="Friend">Friend</option>
                                                        <option value="Other">Other</option>
                                                    </select>
                                                    <label for="payor_relationship">Relationship to Suspect</label>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="form-floating mb-3">
                                            <textarea class="form-control" id="payor_address" name="payor_address" placeholder="Address" style="height: 80px"></textarea>
                                            <label for="payor_address">Address</label>
                                        </div>
                                        
                                        <div class="form-floating mb-3">
                                            <textarea class="form-control" id="remarks" name="remarks" placeholder="Remarks" style="height: 80px"></textarea>
                                            <label for="remarks">Remarks</label>
                                        </div>
                                        
                                        <div class="d-grid">
                                            <button type="submit" name="submit_payment" class="btn btn-primary">
                                                💾 Record Payment
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <div class="col-xl-<?php echo ($remaining_balance > 0) ? '6' : '12'; ?>">
                        <div class="card mb-4">
                            <div class="card-header bg-secondary text-white">
                                <h5 class="mb-0">
                                    📜 Payment History
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($previous_payments)): ?>
                                    <div class="alert alert-info" role="alert">
                                        No payment records found.
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Date</th>
                                                    <th>Amount</th>
                                                    <th>Method</th>
                                                    <th>Payor</th>
                                                    <th>Reference #</th>
                                                    <th>Processed By</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($previous_payments as $payment): ?>
                                                    <tr>
                                                        <td><?php echo date('M d, Y h:i A', strtotime($payment['payment_date'])); ?></td>
                                                        <td>₱<?php echo number_format($payment['amount'], 2); ?></td>
                                                        <td><?php echo htmlspecialchars($payment['payment_method']); ?></td>
                                                        <td>
                                                            <?php echo isset($payment['payer_name']) ? htmlspecialchars($payment['payer_name']) : ''; ?>
                                                            <small class="text-muted d-block"><?php echo isset($payment['payer_relation']) ? htmlspecialchars($payment['payer_relation']) : ''; ?></small>
                                                        </td>
                                                        <td><?php echo htmlspecialchars($payment['reference_number']); ?></td>
                                                        <td><?php echo htmlspecialchars($payment['processed_by_name']); ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                    
                                    <div class="d-flex justify-content-center mt-3">
                                        <a href="print_receipt.php?id=<?php echo $blotter_id; ?>" target="_blank" class="btn btn-outline-primary">
                                            🖨️ Print Payment Receipt
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </main>
    </div>
</div>

<?php include '../../includes/footer.php'; ?>

<!-- Toast container -->
<div class="toast-container position-fixed top-0 end-0 p-3">
    <div id="successToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex align-items-center p-3">
            <div class="me-2">✅</div>
            <div class="me-auto"><?php echo isset($_SESSION['success']) ? $_SESSION['success'] : (isset($_SESSION['error']) ? $_SESSION['error'] : ''); ?></div>
            <div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Default to cash payment method
        if (document.getElementById('payment_method')) {
            document.getElementById('payment_method').value = 'Cash';
        }
        
        // Pre-populate suspect name as payor if self paying
        if (document.getElementById('payor_relationship')) {
            document.getElementById('payor_relationship').addEventListener('change', function() {
                if (this.value === 'Self') {
                    document.getElementById('payor_name').value = '<?php echo addslashes($blotter['suspect_name']); ?>';
                    document.getElementById('payor_address').value = '<?php echo addslashes($blotter['suspect_address']); ?>';
                    document.getElementById('payor_contact').value = '<?php echo addslashes($blotter['suspect_contact']); ?>';
                }
            });
        }
        
        // Form validation
        if (document.getElementById('paymentForm')) {
            const form = document.getElementById('paymentForm');
            form.addEventListener('submit', function(event) {
                const amount = parseFloat(document.getElementById('amount').value);
                const maxAmount = <?php echo $remaining_balance; ?>;
                
                if (amount <= 0 || amount > maxAmount) {
                    event.preventDefault();
                    alert('Please enter a valid amount between ₱1 and ₱' + maxAmount.toFixed(2));
                    document.getElementById('amount').focus();
                }
            });
        }

        // Show toast notification if there's a message
        <?php if(isset($_SESSION['success']) || isset($_SESSION['error'])): ?>
        // Update toast styling based on message type
        const toast = document.getElementById('successToast');
        
        <?php if(isset($_SESSION['success'])): ?>
        toast.style.backgroundColor = '#28a745'; // success green
        toast.style.color = 'white';
        <?php elseif(isset($_SESSION['error'])): ?>
        toast.style.backgroundColor = '#dc3545'; // danger red
        toast.style.color = 'white';
        <?php endif; ?>
        
        const successToast = new bootstrap.Toast(toast, {
            delay: 5000
        });
        successToast.show();

        <?php 
        // Clear session messages after displaying
        if(isset($_SESSION['success'])) unset($_SESSION['success']);
        if(isset($_SESSION['error'])) unset($_SESSION['error']);
        ?>
        <?php endif; ?>
    });
</script> 