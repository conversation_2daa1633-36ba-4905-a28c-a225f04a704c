<?php
// We don't need to include session.php here since it's already included in the parent file

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "<script>window.location.href='../../login.php';</script>";
    exit();
}

// Include permission functions if not already included
if (!function_exists('canAccessModule')) {
    include_once '../../includes/functions/permission_functions.php';
}

// Check if user has permission to access bail module
if (!canAccessModule('bail')) {
    $_SESSION['error'] = 'You do not have permission to access the Bail Management module.';
    echo "<script>window.location.href='../../index.php';</script>";
    exit();
}

// Include required files (don't include header.php, it's already included)
// require_once '../../includes/header.php';

// Add custom bail module CSS
echo '<link rel="stylesheet" href="' . get_absolute_path('modules/bail/bail.css') . '">';

// Add styles for stat cards
echo '<style>
    .stat-card {
        border-radius: 0.75rem;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        z-index: 1;
    }
    .stat-card:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
        z-index: 2;
    }
    .stat-icon {
        font-size: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 60px;
        width: 60px;
        border-radius: 50%;
        margin-bottom: 10px;
        transition: transform 0.3s ease;
    }
    .stat-card:hover .stat-icon {
        transform: scale(1.1);
    }
    
    /* Background colors with opacity */
    .bg-primary-soft { background-color: rgba(78, 115, 223, 0.1) !important; }
    .bg-success-soft { background-color: rgba(28, 200, 138, 0.1) !important; }
    .bg-info-soft { background-color: rgba(54, 185, 204, 0.1) !important; }
    .bg-warning-soft { background-color: rgba(246, 194, 62, 0.1) !important; }
    .bg-danger-soft { background-color: rgba(231, 74, 59, 0.1) !important; }
    .bg-secondary-soft { background-color: rgba(108, 117, 125, 0.1) !important; }
    
    /* Style for emoji headers */
    .emoji-header {
        font-size: 1em;
        opacity: 0.9;
        margin-right: 5px;
    }
    
    /* Style for status badges */
    .badge-emoji {
        padding: 6px 10px;
        font-size: 0.85em;
    }
    
    /* Hide emoji on small screens */
    @media (max-width: 768px) {
        .emoji-header {
            display: none;
        }
        
        .badge .emoji-icon {
            display: none;
        }
    }
</style>';

// Common bail module functions
/**
 * Get status badge HTML based on status value
 * @param string $status The status value
 * @return string HTML for the badge
 */
function getBadgeHtml($status) {
    $badge_class = '';
    $badge_text = $status;
    $emoji = '';
    
    switch (strtolower($status)) {
        case 'active':
            $badge_class = 'bg-success';
            $emoji = '✅';
            break;
        case 'on bail':
            $badge_class = 'bg-success';
            $emoji = '✅';
            break;
        case 'closed':
            $badge_class = 'bg-secondary';
            $emoji = '🔒';
            break;
        case 'transferred':
            $badge_class = 'bg-info';
            $emoji = '🔄';
            break;
        case 'dismissed':
            $badge_class = 'bg-danger';
            $emoji = '❌';
            break;
        case 'bailable':
            $badge_class = 'bg-success';
            $emoji = '💰';
            break;
        case 'non-bailable':
            $badge_class = 'bg-danger';
            $emoji = '🚫';
            break;
        case 'pending':
            $badge_class = 'bg-warning';
            $emoji = '⏳';
            break;
        case 'approved':
            $badge_class = 'bg-success';
            $emoji = '✅';
            break;
        case 'released':
            $badge_class = 'bg-info';
            $emoji = '🔓';
            break;
        case 'completed':
            $badge_class = 'bg-success';
            $emoji = '✓';
            break;
        case 'violation':
            $badge_class = 'bg-danger';
            $emoji = '⚠️';
            break;
        default:
            $badge_class = 'bg-secondary';
            $emoji = '❔';
    }
    
    return '<span class="badge ' . $badge_class . ' badge-emoji">' . $emoji . ' ' . htmlspecialchars($badge_text) . '</span>';
}

/**
 * Format currency value
 * @param float $amount The amount to format
 * @return string Formatted amount with PHP currency symbol
 */
function formatCurrency($amount) {
    return '₱' . number_format($amount, 2);
}

/**
 * Get workflow step class based on current step and target step
 * @param string $current_step Current workflow step
 * @param string $target_step Step to check
 * @return string CSS class for the step
 */
function getWorkflowStepClass($current_step, $target_step) {
    $workflow_steps = [
        'blotter' => 1,
        'assessment' => 2,
        'payment' => 3,
        'release' => 4,
        'monitoring' => 5
    ];
    
    $current_position = $workflow_steps[$current_step] ?? 0;
    $target_position = $workflow_steps[$target_step] ?? 0;
    
    if ($current_position === $target_position) {
        return 'active';
    } elseif ($current_position > $target_position) {
        return 'completed';
    } else {
        return 'pending';
    }
}

/**
 * Get file icon based on file extension
 * @param string $filename Filename with extension
 * @return string Font awesome icon class
 */
function getFileIcon($filename) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    
    switch ($extension) {
        case 'pdf':
            return 'fa-file-pdf';
        case 'doc':
        case 'docx':
            return 'fa-file-word';
        case 'xls':
        case 'xlsx':
            return 'fa-file-excel';
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
            return 'fa-file-image';
        default:
            return 'fa-file';
    }
}
?> 