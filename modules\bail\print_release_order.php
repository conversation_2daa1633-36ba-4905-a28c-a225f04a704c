<?php
// Include database connection
include '../../includes/session.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include permission functions
include '../../includes/functions/permission_functions.php';

// Check if user has permission to access bail module
if (!canAccessModule('bail')) {
    $_SESSION['error'] = 'You do not have permission to access this page.';
    header('Location: ../../index.php');
    exit();
}

// Check if a blotter ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = 'No case specified for printing release order.';
    header('Location: blotter.php');
    exit();
}

$blotter_id = $_GET['id'];

// Fetch release order details
try {
    $stmt = $conn->prepare("
        SELECT ro.*, be.reference_number, be.suspect_name, be.suspect_address, 
               be.arrest_date, be.detention_facility, be.case_type, 
               be.incident_date, be.incident_location, 
               (SELECT SUM(amount) FROM bail_payments WHERE blotter_id = be.blotter_id AND payment_status = 'Completed') as bail_amount_paid,
               ba.bail_amount,
               (SELECT setting_value FROM system_settings WHERE setting_name = 'system_name') as system_name, 
               (SELECT setting_value FROM system_settings WHERE setting_name = 'barangay_name') as barangay_name, 
               (SELECT setting_value FROM system_settings WHERE setting_name = 'municipality') as municipality, 
               (SELECT setting_value FROM system_settings WHERE setting_name = 'province') as province, 
               (SELECT setting_value FROM system_settings WHERE setting_name = 'logo') as logo,
               u.username as processed_by_name,
               u.user_type as officer_position
        FROM release_orders ro
        JOIN blotter_entries be ON ro.blotter_id = be.blotter_id
        LEFT JOIN bail_assessments ba ON be.blotter_id = ba.blotter_id
        LEFT JOIN users u ON ro.processed_by = u.user_id
        WHERE ro.blotter_id = :blotter_id
    ");
    $stmt->bindParam(':blotter_id', $blotter_id);
    $stmt->execute();
    $order = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$order) {
        $_SESSION['error'] = 'Release order not found. Please process a release order first.';
        header('Location: release_order.php?id=' . $blotter_id);
        exit();
    }
    
    // Format dates
    $release_date = date('F d, Y', strtotime($order['release_date']));
    $court_date = date('F d, Y \a\t h:i A', strtotime($order['next_court_date']));
    $arrest_date = date('F d, Y', strtotime($order['arrest_date']));
    $processed_date = date('F d, Y', strtotime($order['processed_at']));
    
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error: ' . $e->getMessage();
    header('Location: view_case.php?id=' . $blotter_id);
    exit();
}

// Determine logo path
$logo_path = "../../logo.jpeg";
if (!file_exists($logo_path)) {
    $logo_path = "../../assets/img/system-logo.png";
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Release Order - <?php echo $order['release_order_number']; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Times New Roman', Times, serif;
            font-size: 12pt;
            line-height: 1.5;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .logo {
            max-height: 80px;
            margin-bottom: 10px;
        }
        .document-title {
            font-size: 18pt;
            font-weight: bold;
            margin: 20px 0;
            text-align: center;
            text-decoration: underline;
        }
        .section {
            margin-bottom: 20px;
        }
        .section-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .indent {
            padding-left: 40px;
        }
        .signature-line {
            border-top: 1px solid #000;
            width: 200px;
            margin-top: 40px;
            margin-bottom: 5px;
        }
        .footer {
            font-size: 10pt;
            margin-top: 30px;
            border-top: 1px solid #ddd;
            padding-top: 10px;
            text-align: center;
        }
        .text-center {
            text-align: center;
        }
        .document-number {
            font-size: 10pt;
            margin-bottom: 20px;
        }
        .page-break {
            page-break-after: always;
        }
        .conditions {
            margin-left: 20px;
        }
        .stamp {
            border: 2px dashed #555;
            padding: 10px;
            width: 200px;
            text-align: center;
            transform: rotate(-5deg);
            font-weight: bold;
            color: #555;
            margin: 20px 0;
        }
        @media print {
            .no-print {
                display: none;
            }
            body {
                margin: 0;
                padding: 15mm;
                font-size: 12pt;
            }
            .container {
                width: 100%;
                max-width: 100%;
                padding: 0;
                margin: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container mt-4 mb-4">
        <div class="no-print mb-4">
            <button class="btn btn-primary" onclick="window.print()">
                <i class="fas fa-print"></i> Print Release Order
            </button>
            <a href="view_case.php?id=<?php echo $blotter_id; ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Case
            </a>
        </div>
        
        <div class="header">
            <div style="text-align: center; margin-bottom: 15px;">
                <img src="../../assets/images/logo.jpeg" alt="Barangay Talisay Logo" class="logo" style="max-height: 120px;">
            </div>
            <h4>Republic of the Philippines</h4>
            <h5>Province of <?php echo $order['province']; ?></h5>
            <h5>Municipality of <?php echo $order['municipality']; ?></h5>
            <h5>Barangay <?php echo $order['barangay_name']; ?></h5>
        </div>
        
        <div class="document-title">ORDER OF RELEASE ON BAIL</div>
        
        <div class="document-number text-center">
            Release Order No.: <strong><?php echo $order['release_order_number']; ?></strong>
        </div>
        
        <div class="section">
            <p><strong>TO THE OFFICER-IN-CHARGE:</strong><br>
            <?php echo htmlspecialchars($order['detention_facility']); ?></p>
            
            <p class="mt-4">GREETINGS:</p>
            
            <p class="mt-4 text-justify">
                WHEREAS, <strong><?php echo htmlspecialchars($order['suspect_name']); ?></strong> was arrested and detained on 
                <strong><?php echo $arrest_date; ?></strong> for the alleged offense of <strong><?php echo htmlspecialchars($order['case_type']); ?></strong>;
            </p>
            
            <p class="text-justify">
                WHEREAS, the said detained person has applied for bail and has been assessed by this office as eligible for posting bail in the amount of 
                <strong>₱<?php echo number_format($order['bail_amount'], 2); ?></strong> which has been fully paid and received by this office;
            </p>
            
            <p class="text-justify">
                NOW THEREFORE, by virtue of the authority vested in me by law, I hereby order you to <strong>RELEASE</strong> 
                <strong><?php echo htmlspecialchars($order['suspect_name']); ?></strong> from your custody, unless held for some other lawful cause, 
                subject to the following conditions:
            </p>
            
            <div class="conditions">
                <?php echo nl2br(htmlspecialchars($order['release_conditions'])); ?>
            </div>
        </div>
        
        <div class="section">
            <p><strong>TAKE NOTE</strong> that the accused is required to appear before the proper court on <strong><?php echo $court_date; ?></strong> 
            at <strong><?php echo htmlspecialchars($order['court_location']); ?></strong> and whenever required by the court.</p>
            
            <p>Failure to comply with any of these conditions shall result in the forfeiture of bail and possible issuance of a warrant of arrest.</p>
        </div>
        
        <?php if ($order['release_status'] == 'Released'): ?>
        <div class="stamp">
            RELEASED<br>
            <?php echo date('m/d/Y', strtotime($order['release_date'])); ?>
        </div>
        <?php elseif ($order['release_status'] == 'Approved'): ?>
        <div class="stamp">
            APPROVED<br>
            AWAITING RELEASE
        </div>
        <?php elseif ($order['release_status'] == 'Pending'): ?>
        <div class="stamp">
            PENDING<br>
            FOR APPROVAL
        </div>
        <?php endif; ?>
        
        <div class="section">
            <p>Given this <?php echo date('jS \d\a\y \of F, Y', strtotime($order['processed_at'])); ?> at Barangay <?php echo $order['barangay_name']; ?>, 
            <?php echo $order['municipality']; ?>, <?php echo $order['province']; ?>.</p>
        </div>
        
        <div class="row mt-5">
            <div class="col-6">
            </div>
            <div class="col-6 text-center">
                <div class="signature-line mx-auto"></div>
                <p><strong><?php echo strtoupper($order['processed_by_name']); ?></strong><br>
                <?php echo $order['officer_position']; ?><br>
                Issuing Authority</p>
            </div>
        </div>
        
        <div class="section mt-5">
            <div class="row">
                <div class="col-6">
                    <p><strong>Reference:</strong><br>
                    Blotter Entry: <?php echo $order['reference_number']; ?><br>
                    Bail Amount: ₱<?php echo number_format($order['bail_amount_paid'], 2); ?><br>
                    Date Processed: <?php echo $processed_date; ?></p>
                </div>
                <div class="col-6">
                    <?php if (!empty($order['remarks'])): ?>
                    <p><strong>Remarks:</strong><br>
                    <?php echo nl2br(htmlspecialchars($order['remarks'])); ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>This is an official document of <?php echo $order['barangay_name']; ?> Barangay Management Office.<br>
            Any alteration or misrepresentation of this document is punishable by law.</p>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto print when page loads
        window.onload = function() {
            // Slight delay to ensure everything renders
            setTimeout(function() {
                window.print();
            }, 500);
        };
    </script>
</body>
</html> 