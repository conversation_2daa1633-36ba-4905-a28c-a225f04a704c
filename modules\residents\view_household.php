<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/utility.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission
if (!hasPermission('view_households')) {
    header("Location: ../../index.php");
    exit;
}

// Get household ID from URL
$household_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($household_id <= 0) {
    header("Location: households.php");
    exit;
}

// Get household information
$query = "SELECT h.*, 
          CONCAT(r.last_name, ', ', r.first_name, ' ', COALESCE(r.middle_name, '')) as head_name,
          r.resident_id as head_id
          FROM households h
          LEFT JOIN residents r ON h.household_head_id = r.resident_id
          WHERE h.household_id = :household_id";
$stmt = $conn->prepare($query);
$stmt->bindParam(':household_id', $household_id, PDO::PARAM_INT);
$stmt->execute();

if ($stmt->rowCount() == 0) {
    // Household not found
    header("Location: households.php?error=not_found");
    exit;
}

$household = $stmt->fetch(PDO::FETCH_ASSOC);

// Get household members
$members_query = "SELECT hm.*, 
                  CONCAT(r.last_name, ', ', r.first_name, ' ', COALESCE(r.middle_name, '')) as full_name,
                  r.gender, r.birthdate, r.civil_status, r.occupation, r.contact_number, r.email
                  FROM household_members hm
                  JOIN residents r ON hm.resident_id = r.resident_id
                  WHERE hm.household_id = :household_id
                  ORDER BY hm.relation_to_head = 'Head' DESC, r.last_name, r.first_name";
$members_stmt = $conn->prepare($members_query);
$members_stmt->bindParam(':household_id', $household_id, PDO::PARAM_INT);
$members_stmt->execute();
$members = $members_stmt->fetchAll(PDO::FETCH_ASSOC);

// Check for success message
$success = isset($_GET['success']) ? $_GET['success'] : '';
$success_message = '';

if ($success == '1') {
    $success_message = "Household has been saved successfully.";
} elseif ($success == '2') {
    $success_message = "Household has been updated successfully.";
}

// Page title
$page_title = "Household Details: " . $household['household_code'] . " - Barangay Management System";

// Log this view
if (function_exists('logActivity')) {
    logActivity("Viewed household: " . $household['household_code'], $_SESSION['user_id'], 'view', 'households', $household_id);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        /* Toast notification styles */
        .toast-container {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 9999;
        }
        
        .toast {
            background-color: white;
            width: 350px;
            max-width: 100%;
            font-size: 0.875rem;
            pointer-events: auto;
            background-clip: padding-box;
            border: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border-radius: 0.25rem;
            opacity: 0;
            transition: transform .15s ease-in-out, opacity .15s ease-in-out;
            transform: translateY(100%);
        }
        
        .toast.showing {
            opacity: 1;
            transform: translateY(0);
        }
        
        .toast.show {
            display: block;
            opacity: 1;
            transform: translateY(0);
        }
        
        .toast.hide {
            display: none;
        }
        
        .toast-header {
            display: flex;
            align-items: center;
            padding: 0.5rem 0.75rem;
            color: #6c757d;
            background-color: rgba(255, 255, 255, 0.85);
            background-clip: padding-box;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            border-top-left-radius: calc(0.25rem - 1px);
            border-top-right-radius: calc(0.25rem - 1px);
        }
        
        .toast-body {
            padding: 0.75rem;
        }
        
        .me-auto {
            margin-right: auto !important;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">🏠 Household Details</h1>
                    <div>
                        <a href="households.php" class="btn btn-secondary">
                            ⬅️ Back to List
                        </a>
                        <?php if (hasPermission('edit_household')): ?>
                        <a href="edit_household.php?id=<?php echo $household_id; ?>" class="btn btn-primary">
                            ✏️ Edit Household
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
                
                
                
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card h-100 border-left-primary shadow">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0 text-primary">🏠 Household Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-4 fw-bold">🔢 Household Code:</div>
                                    <div class="col-md-8">
                                        <span class="badge bg-primary"><?php echo htmlspecialchars($household['household_code']); ?></span>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4 fw-bold">📍 Address:</div>
                                    <div class="col-md-8"><?php echo htmlspecialchars($household['address']); ?></div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4 fw-bold">🗿 Landmark:</div>
                                    <div class="col-md-8"><?php echo htmlspecialchars($household['landmark'] ?? 'N/A'); ?></div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4 fw-bold">📅 Years of Residency:</div>
                                    <div class="col-md-8"><?php echo $household['years_of_residency'] ? $household['years_of_residency'] . ' years' : 'N/A'; ?></div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4 fw-bold">🏘️ House Ownership:</div>
                                    <div class="col-md-8"><?php echo htmlspecialchars($household['house_ownership']); ?></div>
                                </div>
                                <?php if ($household['house_ownership'] == 'Rented'): ?>
                                <div class="row mb-3">
                                    <div class="col-md-4 fw-bold">💰 Monthly Rent:</div>
                                    <div class="col-md-8">₱ <?php echo number_format($household['monthly_rent'], 2); ?></div>
                                </div>
                                <?php endif; ?>
                                <div class="row mb-3">
                                    <div class="col-md-4 fw-bold">💼 Economic Status:</div>
                                    <div class="col-md-8"><?php echo empty($household['economic_status']) ? 'Not specified' : htmlspecialchars($household['economic_status']); ?></div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4 fw-bold">📊 Status:</div>
                                    <div class="col-md-8">
                                        <span class="badge <?php echo $household['status'] == 'Active' ? 'bg-success' : 'bg-warning text-dark'; ?>">
                                            <?php echo $household['status'] == 'Active' ? '✅ ' : '⚠️ '; ?><?php echo ucfirst($household['status']); ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4 fw-bold">🔌 Utilities:</div>
                                    <div class="col-md-8">
                                        <?php if ($household['has_electricity']): ?>
                                        <span class="badge bg-success me-1">💡 Electricity</span>
                                        <?php endif; ?>
                                        
                                        <?php if ($household['has_water_supply']): ?>
                                        <span class="badge bg-info me-1">🚿 Water Supply</span>
                                        <?php endif; ?>
                                        
                                        <?php if ($household['has_internet']): ?>
                                        <span class="badge bg-primary">🌐 Internet</span>
                                        <?php endif; ?>
                                        
                                        <?php if (!$household['has_electricity'] && !$household['has_water_supply'] && !$household['has_internet']): ?>
                                        <span class="text-muted">None</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card h-100 border-left-success shadow">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0 text-success">👨‍👩‍👧‍👦 Household Head Information</h5>
                            </div>
                            <div class="card-body">
                                <?php if ($household['head_id']): ?>
                                <div class="row mb-3">
                                    <div class="col-md-4 fw-bold">👤 Name:</div>
                                    <div class="col-md-8">
                                        <a href="../residents/view_resident.php?id=<?php echo $household['head_id']; ?>">
                                            <?php echo htmlspecialchars($household['head_name']); ?>
                                        </a>
                                    </div>
                                </div>
                                
                                <?php 
                                // Find head's info from members array
                                $head_info = null;
                                foreach ($members as $member) {
                                    if ($member['resident_id'] == $household['head_id']) {
                                        $head_info = $member;
                                        break;
                                    }
                                }
                                
                                if ($head_info):
                                ?>
                                <div class="row mb-3">
                                    <div class="col-md-4 fw-bold">⚧️ Gender:</div>
                                    <div class="col-md-8"><?php echo $head_info['gender']; ?></div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4 fw-bold">🎂 Age:</div>
                                    <div class="col-md-8">
                                        <?php 
                                        $birthdate = new DateTime($head_info['birthdate']);
                                        $today = new DateTime();
                                        echo $birthdate->diff($today)->y . ' years';
                                        ?>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4 fw-bold">💍 Civil Status:</div>
                                    <div class="col-md-8"><?php echo $head_info['civil_status']; ?></div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4 fw-bold">💼 Occupation:</div>
                                    <div class="col-md-8"><?php echo htmlspecialchars($head_info['occupation'] ?? 'N/A'); ?></div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4 fw-bold">📱 Contact:</div>
                                    <div class="col-md-8"><?php echo htmlspecialchars($head_info['contact_number'] ?? 'N/A'); ?></div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4 fw-bold">📧 Email:</div>
                                    <div class="col-md-8"><?php echo htmlspecialchars($head_info['email'] ?? 'N/A'); ?></div>
                                </div>
                                <?php else: ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i> Detailed information about household head is not available.
                                </div>
                                <?php endif; ?>
                                <?php else: ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle"></i> No household head is assigned to this household.
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card mb-4 border-left-info shadow">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0 text-info">👪 Household Members (<?php echo count($members); ?>)</h5>
                        <?php if (hasPermission('edit_household')): ?>
                        <a href="edit_household_members.php?id=<?php echo $household_id; ?>" class="btn btn-sm btn-primary">
                            👥 Manage Members
                        </a>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <?php if (empty($members)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> No members found for this household.
                        </div>
                        <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>👤 Name</th>
                                        <th>👪 Relation</th>
                                        <th>⚧️ Gender</th>
                                        <th>🎂 Age</th>
                                        <th>💍 Civil Status</th>
                                        <th>💼 Occupation</th>
                                        <th>📱 Contact</th>
                                        <th>🔍 Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($members as $member): ?>
                                    <tr>
                                        <td>
                                            <a href="../residents/view_resident.php?id=<?php echo $member['resident_id']; ?>">
                                                <?php echo htmlspecialchars($member['full_name']); ?>
                                            </a>
                                        </td>
                                        <td><?php echo htmlspecialchars($member['relation_to_head']); ?></td>
                                        <td><?php echo $member['gender']; ?></td>
                                        <td>
                                            <?php 
                                            $birthdate = new DateTime($member['birthdate']);
                                            $today = new DateTime();
                                            echo $birthdate->diff($today)->y;
                                            ?>
                                        </td>
                                        <td><?php echo $member['civil_status']; ?></td>
                                        <td><?php echo htmlspecialchars($member['occupation'] ?? 'N/A'); ?></td>
                                        <td><?php echo htmlspecialchars($member['contact_number'] ?? 'N/A'); ?></td>
                                        <td>
                                            <a href="../residents/view_resident.php?id=<?php echo $member['resident_id']; ?>" class="btn btn-sm btn-info">
                                                👁️ View
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Toast container -->
    <div class="toast-container position-fixed top-0 end-0 p-3">
        <div id="successToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex align-items-center p-3">
                <div class="me-2">✅</div>
                <div class="me-auto"><?php echo $success_message; ?></div>
                <div>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Show toast notification if there's a success message
        document.addEventListener('DOMContentLoaded', function() {
            <?php if(!empty($success_message)): ?>
            // Update toast styling to be full green
            const toast = document.getElementById('successToast');
            toast.style.backgroundColor = '#28a745';
            toast.style.color = 'white';
            
            const successToast = new bootstrap.Toast(toast, {
                delay: 5000
            });
            successToast.show();
            <?php endif; ?>
        });
    </script>
</body>
</html> 