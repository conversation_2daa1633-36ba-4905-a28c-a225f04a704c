<?php
session_start();
include 'includes/config/database.php';
include 'includes/functions/permission_functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "Please login first";
    exit;
}

echo "<h2>Current User Information</h2>";
echo "User ID: " . $_SESSION['user_id'] . "<br>";
echo "User Type: " . $_SESSION['user_type'] . "<br>";

// Get role_id for current user type
$role_id = getUserTypeRoleId($_SESSION['user_type']);
echo "Role ID: " . $role_id . "<br><br>";

echo "<h3>Migration-related Permissions Check:</h3>";
$migration_permissions = [
    'view_residents',
    'edit_residents', 
    'view_migration',
    'add_migration',
    'edit_migration',
    'delete_migration',
    'manage_migration'
];

foreach ($migration_permissions as $permission) {
    $has_permission = hasPermission($permission);
    echo $permission . ": " . ($has_permission ? "✅ YES" : "❌ NO") . "<br>";
}

echo "<br><h3>All Permissions for this Role:</h3>";
try {
    $stmt = $conn->prepare("
        SELECT p.module, p.permission_name, p.description 
        FROM permissions p
        INNER JOIN user_permissions up ON p.permission_id = up.permission_id
        WHERE up.role_id = ? AND p.status = 'active'
        ORDER BY p.module, p.permission_name
    ");
    $stmt->execute([$role_id]);
    $permissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($permissions)) {
        echo "No permissions found for this role.";
    } else {
        $current_module = '';
        foreach ($permissions as $perm) {
            if ($perm['module'] != $current_module) {
                if ($current_module != '') echo "<br>";
                echo "<strong>" . ucfirst($perm['module']) . " Module:</strong><br>";
                $current_module = $perm['module'];
            }
            echo "- " . $perm['permission_name'] . " (" . $perm['description'] . ")<br>";
        }
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
