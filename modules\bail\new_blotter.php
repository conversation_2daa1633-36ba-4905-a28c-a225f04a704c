<?php
// Include database connection
include '../../includes/session.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include permission functions
include_once '../../includes/functions/permission_functions.php';

// Check if user has permission to access bail module
if (!canAccessModule('bail')) {
    $_SESSION['error'] = 'You do not have permission to access the Bail Management module.';
    header('Location: ../../index.php');
    exit();
}

// Set page title
$page_title = 'New Blotter Entry';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Get form data
        $reference_number = 'BR-' . date('Ymd') . '-' . substr(uniqid(), -4);
        $suspect_name = $_POST['suspect_name'];
        $suspect_address = $_POST['suspect_address'];
        $suspect_contact = $_POST['suspect_contact'];
        $complainant_name = $_POST['complainant_name'];
        $complainant_address = $_POST['complainant_address'];
        $complainant_contact = $_POST['complainant_contact'];
        $case_type = $_POST['case_type'];
        $incident_location = $_POST['incident_location'];
        $incident_date = $_POST['incident_date'];
        $incident_details = $_POST['incident_details'];
        $status = 'Active';
        $arresting_officer = $_POST['arresting_officer'];
        $arrest_date = $_POST['arrest_date'];
        $arrest_location = $_POST['arrest_location'];
        $detention_facility = $_POST['detention_facility'];
        $created_by = $_SESSION['user_id'];
        $created_at = date('Y-m-d H:i:s');

        // Insert into blotter_entries table
        $sql = "INSERT INTO blotter_entries (
                    reference_number, suspect_name, suspect_address, suspect_contact, 
                    complainant_name, complainant_address, complainant_contact, 
                    case_type, incident_location, incident_date, incident_details, 
                    status, arresting_officer, arrest_date, arrest_location, 
                    detention_facility, created_by, created_at
                ) VALUES (
                    :reference_number, :suspect_name, :suspect_address, :suspect_contact, 
                    :complainant_name, :complainant_address, :complainant_contact, 
                    :case_type, :incident_location, :incident_date, :incident_details, 
                    :status, :arresting_officer, :arrest_date, :arrest_location, 
                    :detention_facility, :created_by, :created_at
                )";
        
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':reference_number', $reference_number);
        $stmt->bindParam(':suspect_name', $suspect_name);
        $stmt->bindParam(':suspect_address', $suspect_address);
        $stmt->bindParam(':suspect_contact', $suspect_contact);
        $stmt->bindParam(':complainant_name', $complainant_name);
        $stmt->bindParam(':complainant_address', $complainant_address);
        $stmt->bindParam(':complainant_contact', $complainant_contact);
        $stmt->bindParam(':case_type', $case_type);
        $stmt->bindParam(':incident_location', $incident_location);
        $stmt->bindParam(':incident_date', $incident_date);
        $stmt->bindParam(':incident_details', $incident_details);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':arresting_officer', $arresting_officer);
        $stmt->bindParam(':arrest_date', $arrest_date);
        $stmt->bindParam(':arrest_location', $arrest_location);
        $stmt->bindParam(':detention_facility', $detention_facility);
        $stmt->bindParam(':created_by', $created_by);
        $stmt->bindParam(':created_at', $created_at);
        
        if ($stmt->execute()) {
            $blotter_id = $conn->lastInsertId();
            $_SESSION['success'] = 'Blotter entry created successfully with Reference # ' . $reference_number;

            // If immediate assessment is requested
            if (isset($_POST['proceed_to_assessment']) && $_POST['proceed_to_assessment'] == '1') {
                header('Location: bail_assessment.php?id=' . $blotter_id);
                exit();
            } else {
                header('Location: blotter.php');
                exit();
            }
        } else {
            $_SESSION['error'] = 'Failed to create blotter entry.';
        }
    } catch (PDOException $e) {
        $_SESSION['error'] = 'Database error: ' . $e->getMessage();
    }
}

// Get residents list for dropdown
try {
    $stmt = $conn->query("SELECT resident_id, CONCAT(first_name, ' ', last_name) as full_name, address, contact_number FROM residents ORDER BY last_name");
    $residents = $stmt->fetchAll();
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error: ' . $e->getMessage();
    $residents = [];
}

// Include header and sidebar
include '../../includes/header.php';
include '../../includes/sidebar.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="mt-4">New Blotter Entry</h1>
            <ol class="breadcrumb mb-4">
                <li class="breadcrumb-item"><a href="../../index.php">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="index.php">Bail Management</a></li>
                <li class="breadcrumb-item"><a href="blotter.php">Blotter Entries</a></li>
                <li class="breadcrumb-item active">New Entry</li>
            </ol>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php 
                    echo $_SESSION['success']; 
                    unset($_SESSION['success']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php 
                    echo $_SESSION['error']; 
                    unset($_SESSION['error']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-clipboard-list me-1"></i>
                    New Blotter Entry Form
                </div>
                <div class="card-body">
                    <form method="POST" action="new_blotter.php" id="blotterForm">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0">Suspect Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-floating mb-3">
                                            <select class="form-select" id="suspect_select" onchange="populateSuspectInfo()">
                                                <option value="">Select existing resident (optional)</option>
                                                <?php foreach ($residents as $resident): ?>
                                                    <option value="<?php echo $resident['resident_id']; ?>" 
                                                            data-name="<?php echo htmlspecialchars($resident['full_name']); ?>"
                                                            data-address="<?php echo htmlspecialchars($resident['address']); ?>"
                                                            data-contact="<?php echo htmlspecialchars($resident['contact_number']); ?>">
                                                        <?php echo htmlspecialchars($resident['full_name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <label for="suspect_select">Select Existing Resident</label>
                                        </div>
                                        
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="suspect_name" name="suspect_name" placeholder="Full Name" required>
                                            <label for="suspect_name">Full Name</label>
                                        </div>
                                        
                                        <div class="form-floating mb-3">
                                            <textarea class="form-control" id="suspect_address" name="suspect_address" placeholder="Address" style="height: 100px" required></textarea>
                                            <label for="suspect_address">Address</label>
                                        </div>
                                        
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="suspect_contact" name="suspect_contact" placeholder="Contact Number">
                                            <label for="suspect_contact">Contact Number</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="mb-0">Complainant Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-floating mb-3">
                                            <select class="form-select" id="complainant_select" onchange="populateComplainantInfo()">
                                                <option value="">Select existing resident (optional)</option>
                                                <?php foreach ($residents as $resident): ?>
                                                    <option value="<?php echo $resident['resident_id']; ?>" 
                                                            data-name="<?php echo htmlspecialchars($resident['full_name']); ?>"
                                                            data-address="<?php echo htmlspecialchars($resident['address']); ?>"
                                                            data-contact="<?php echo htmlspecialchars($resident['contact_number']); ?>">
                                                        <?php echo htmlspecialchars($resident['full_name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <label for="complainant_select">Select Existing Resident</label>
                                        </div>
                                        
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="complainant_name" name="complainant_name" placeholder="Full Name" required>
                                            <label for="complainant_name">Full Name</label>
                                        </div>
                                        
                                        <div class="form-floating mb-3">
                                            <textarea class="form-control" id="complainant_address" name="complainant_address" placeholder="Address" style="height: 100px" required></textarea>
                                            <label for="complainant_address">Address</label>
                                        </div>
                                        
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="complainant_contact" name="complainant_contact" placeholder="Contact Number">
                                            <label for="complainant_contact">Contact Number</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header bg-secondary text-white">
                                        <h5 class="mb-0">Incident Details</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-floating mb-3">
                                                    <select class="form-select" id="case_type" name="case_type" required>
                                                        <option value="">Select case type</option>
                                                        <option value="Theft">Theft</option>
                                                        <option value="Physical Assault">Physical Assault</option>
                                                        <option value="Robbery">Robbery</option>
                                                        <option value="Drug-related">Drug-related</option>
                                                        <option value="Domestic Violence">Domestic Violence</option>
                                                        <option value="Public Disturbance">Public Disturbance</option>
                                                        <option value="Property Damage">Property Damage</option>
                                                        <option value="Traffic Violation">Traffic Violation</option>
                                                        <option value="Fraud">Fraud</option>
                                                        <option value="Other">Other</option>
                                                    </select>
                                                    <label for="case_type">Case Type</label>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-4">
                                                <div class="form-floating mb-3">
                                                    <input type="text" class="form-control" id="incident_location" name="incident_location" placeholder="Incident Location" required>
                                                    <label for="incident_location">Incident Location</label>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-4">
                                                <div class="form-floating mb-3">
                                                    <input type="datetime-local" class="form-control" id="incident_date" name="incident_date" required>
                                                    <label for="incident_date">Incident Date & Time</label>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="form-floating mb-3">
                                            <textarea class="form-control" id="incident_details" name="incident_details" placeholder="Incident Details" style="height: 150px" required></textarea>
                                            <label for="incident_details">Incident Details</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header bg-danger text-white">
                                        <h5 class="mb-0">Arrest Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-floating mb-3">
                                                    <input type="text" class="form-control" id="arresting_officer" name="arresting_officer" placeholder="Arresting Officer" required>
                                                    <label for="arresting_officer">Arresting Officer</label>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-4">
                                                <div class="form-floating mb-3">
                                                    <input type="datetime-local" class="form-control" id="arrest_date" name="arrest_date" required>
                                                    <label for="arrest_date">Arrest Date & Time</label>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-4">
                                                <div class="form-floating mb-3">
                                                    <input type="text" class="form-control" id="arrest_location" name="arrest_location" placeholder="Arrest Location" required>
                                                    <label for="arrest_location">Arrest Location</label>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="detention_facility" name="detention_facility" placeholder="Detention Facility" required>
                                            <label for="detention_facility">Detention Facility</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="proceed_to_assessment" name="proceed_to_assessment" value="1">
                            <label class="form-check-label" for="proceed_to_assessment">
                                Proceed directly to bail assessment after saving
                            </label>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="blotter.php" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">Save Blotter Entry</button>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../../includes/footer.php'; ?>

<script>
    function populateSuspectInfo() {
        const select = document.getElementById('suspect_select');
        const selectedOption = select.options[select.selectedIndex];
        
        if (select.value !== '') {
            document.getElementById('suspect_name').value = selectedOption.getAttribute('data-name');
            document.getElementById('suspect_address').value = selectedOption.getAttribute('data-address');
            document.getElementById('suspect_contact').value = selectedOption.getAttribute('data-contact');
        } else {
            document.getElementById('suspect_name').value = '';
            document.getElementById('suspect_address').value = '';
            document.getElementById('suspect_contact').value = '';
        }
    }
    
    function populateComplainantInfo() {
        const select = document.getElementById('complainant_select');
        const selectedOption = select.options[select.selectedIndex];
        
        if (select.value !== '') {
            document.getElementById('complainant_name').value = selectedOption.getAttribute('data-name');
            document.getElementById('complainant_address').value = selectedOption.getAttribute('data-address');
            document.getElementById('complainant_contact').value = selectedOption.getAttribute('data-contact');
        } else {
            document.getElementById('complainant_name').value = '';
            document.getElementById('complainant_address').value = '';
            document.getElementById('complainant_contact').value = '';
        }
    }
    
    document.addEventListener('DOMContentLoaded', function() {
        // Set current date and time as default for incident and arrest date
        const now = new Date();
        const formattedDateTime = now.toISOString().slice(0, 16);
        document.getElementById('incident_date').value = formattedDateTime;
        document.getElementById('arrest_date').value = formattedDateTime;
        
        // Form validation
        const form = document.getElementById('blotterForm');
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
</script> 