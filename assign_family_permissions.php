<?php
session_start();
include 'includes/config/database.php';
include 'includes/functions/permission_functions.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] != 'Admin') {
    echo "Only Admin can run this script";
    exit;
}

echo "<h2>👨‍👩‍👧‍👦 Assign Family Relationship Permissions to Secretary Role</h2>";

try {
    // Get Secretary role_id
    $secretary_role_id = getUserTypeRoleId('Secretary');
    if (!$secretary_role_id) {
        echo "❌ Secretary role not found<br>";
        exit;
    }
    echo "✅ Secretary Role ID: " . $secretary_role_id . "<br><br>";

    // Define family relationship permissions to assign
    $family_permissions = [
        'view_family_relationship' => 'View Family Relationships',
        'add_family_relationship' => 'Add Family Relationships', 
        'edit_family_relationship' => 'Edit Family Relationships',
        'delete_family_relationship' => 'Delete Family Relationships'
    ];

    $conn->beginTransaction();

    foreach ($family_permissions as $permission_name => $description) {
        // Check if permission exists
        $check_perm = $conn->prepare("SELECT permission_id FROM permissions WHERE permission_name = ? AND module = ?");
        $check_perm->execute([$permission_name, 'households']);
        $permission = $check_perm->fetch(PDO::FETCH_ASSOC);
        
        if (!$permission) {
            // Create permission if it doesn't exist
            $create_perm = $conn->prepare("INSERT INTO permissions (role_id, module, permission_name, description, status) VALUES (?, ?, ?, ?, 'active')");
            $create_perm->execute([$secretary_role_id, 'households', $permission_name, $description]);
            $permission_id = $conn->lastInsertId();
            echo "✅ Created permission: " . $permission_name . "<br>";
        } else {
            $permission_id = $permission['permission_id'];
            echo "ℹ️ Permission exists: " . $permission_name . "<br>";
        }

        // Check if user_permission assignment exists
        $check_assignment = $conn->prepare("SELECT * FROM user_permissions WHERE role_id = ? AND permission_id = ?");
        $check_assignment->execute([$secretary_role_id, $permission_id]);
        
        if ($check_assignment->rowCount() == 0) {
            // Assign permission to Secretary role
            $assign_perm = $conn->prepare("INSERT INTO user_permissions (role_id, permission_id) VALUES (?, ?)");
            $assign_perm->execute([$secretary_role_id, $permission_id]);
            echo "✅ Assigned " . $permission_name . " to Secretary role<br>";
        } else {
            echo "ℹ️ Permission already assigned: " . $permission_name . "<br>";
        }
    }

    $conn->commit();
    echo "<br><strong>✅ All family relationship permissions have been assigned to Secretary role!</strong><br>";
    echo "<br><a href='modules/residents/family_relationships.php'>Test Family Relationships Page</a><br>";
    echo "<a href='check_secretary_permissions.php'>Check Secretary Permissions</a><br>";

} catch (Exception $e) {
    $conn->rollBack();
    echo "❌ Error: " . $e->getMessage();
}
?>
