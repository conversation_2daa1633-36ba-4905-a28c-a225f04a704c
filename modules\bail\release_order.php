<?php
// Include database connection - only once
include_once '../../includes/session.php';

// Include header and sidebar first to prevent header errors
include_once '../../includes/header.php';
include_once '../../includes/sidebar.php';
include_once 'bail_header.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    $_SESSION['error'] = 'You must be logged in to access the Release Order page.';
    echo "<script>window.location.href='../../login.php';</script>";
    exit();
}

// Include permission functions
include_once '../../includes/functions/permission_functions.php';

// Check if user has permission to access bail module
if (!canAccessModule('bail')) {
    $_SESSION['error'] = 'You do not have permission to access the Release Order page.';
    echo "<script>window.location.href='../../index.php';</script>";
    exit();
}

// Set page title
$page_title = 'Release Order';

// Check if a blotter ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    // Instead of redirecting, show a list of cases eligible for release orders
    $list_mode = true;
    
    // Get cases that have full bail payment and are eligible for release
    try {
        $stmt = $conn->query("
            SELECT be.*, ba.is_bailable, ba.bail_amount, 
                   (SELECT SUM(amount) FROM bail_payments WHERE blotter_id = be.blotter_id AND payment_status = 'Completed') as total_paid,
                   (SELECT COUNT(*) FROM release_orders WHERE blotter_id = be.blotter_id) as has_release_order
            FROM blotter_entries be
            JOIN bail_assessments ba ON be.blotter_id = ba.blotter_id
            WHERE be.status IN ('Active', 'On Bail') 
              AND ba.is_bailable = 1
            HAVING (total_paid >= bail_amount OR total_paid IS NULL)
            ORDER BY has_release_order ASC, be.created_at DESC
        ");
        $release_cases = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $_SESSION['error'] = 'Database error: ' . $e->getMessage();
        $release_cases = [];
    }
} else {
    $list_mode = false;
    $blotter_id = $_GET['id'];

    // Fetch blotter entry and bail details
    try {
        $stmt = $conn->prepare("
            SELECT be.*, ba.is_bailable, ba.bail_amount,
                   (SELECT SUM(amount) FROM bail_payments WHERE blotter_id = be.blotter_id AND payment_status = 'Completed') as total_paid,
                   (SELECT ro.release_id FROM release_orders ro WHERE ro.blotter_id = be.blotter_id LIMIT 1) as release_id
            FROM blotter_entries be
            LEFT JOIN bail_assessments ba ON be.blotter_id = ba.blotter_id
            WHERE be.blotter_id = :blotter_id
        ");
        $stmt->bindParam(':blotter_id', $blotter_id);
        $stmt->execute();
        $blotter = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$blotter) {
            $_SESSION['error'] = 'Blotter entry not found.';
            echo "<script>window.location.href='blotter.php';</script>";
            exit();
        }

        // Check if case is bailable and bail has been paid
        $total_paid = $blotter['total_paid'] ?: 0;
        $bail_amount = $blotter['bail_amount'] ?: 0;
        
        if (!$blotter['is_bailable']) {
            $_SESSION['error'] = 'This case is not eligible for bail.';
            echo "<script>window.location.href='view_case.php?id=" . $blotter_id . "';</script>";
            exit();
        }
        
        if ($total_paid < $bail_amount) {
            $_SESSION['error'] = 'Bail has not been fully paid. Please complete payment before processing release order.';
            echo "<script>window.location.href='bail_payment.php?id=" . $blotter_id . "';</script>";
            exit();
        }

        // If a release order already exists, fetch its details
        if ($blotter['release_id']) {
            $stmt = $conn->prepare("
                SELECT ro.*, u1.username as processed_by_name, u2.username as approved_by_name
                FROM release_orders ro
                LEFT JOIN users u1 ON ro.processed_by = u1.user_id
                LEFT JOIN users u2 ON ro.approved_by = u2.user_id
                WHERE ro.release_id = :release_order_id
            ");
            $stmt->bindParam(':release_order_id', $blotter['release_id']);
            $stmt->execute();
            $release_order = $stmt->fetch(PDO::FETCH_ASSOC);
        } else {
            $release_order = null;
        }

    } catch (PDOException $e) {
        $_SESSION['error'] = 'Database error: ' . $e->getMessage();
        echo "<script>window.location.href='blotter.php';</script>";
        exit();
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Generate release order number if not existing
        $release_order_number = isset($_POST['release_order_number']) ? 
            $_POST['release_order_number'] : 'RO-' . date('Ymd') . '-' . substr(uniqid(), -4);
        
        $release_date = $_POST['release_date'];
        $release_conditions = $_POST['release_conditions'];
        $next_court_date = $_POST['next_court_date'];
        $court_location = $_POST['court_location'];
        $remarks = $_POST['remarks'];
        $release_status = $_POST['release_status'];
        $processed_by = $_SESSION['user_id'];
        $processed_at = date('Y-m-d H:i:s');
        
        // Handle approved_by and approved_at fields based on status
        $approved_by = null;
        $approved_at = null;
        
        if ($release_status == 'Approved' || $release_status == 'Released') {
            $approved_by = $_SESSION['user_id'];
            $approved_at = date('Y-m-d H:i:s');
        }

        // Check if updating existing or creating new
        if (isset($blotter['release_id']) && $blotter['release_id']) {
            // Update existing release order
            $sql = "UPDATE release_orders SET 
                    release_date = :release_date,
                    release_conditions = :release_conditions,
                    next_court_date = :next_court_date,
                    court_location = :court_location,
                    remarks = :remarks,
                    release_status = :release_status,
                    processed_by = :processed_by,
                    processed_at = :processed_at,
                    approved_by = :approved_by,
                    approved_at = :approved_at
                WHERE release_id = :release_id";
            
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':release_id', $blotter['release_id']);
        } else {
            // Create new release order
            $sql = "INSERT INTO release_orders (
                    blotter_id, release_order_number, release_date,
                    release_conditions, next_court_date, court_location, remarks,
                    release_status, processed_by, processed_at, approved_by, approved_at
                ) VALUES (
                    :blotter_id, :release_order_number, :release_date,
                    :release_conditions, :next_court_date, :court_location, :remarks,
                    :release_status, :processed_by, :processed_at, :approved_by, :approved_at
                )";
            
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':blotter_id', $blotter_id);
            $stmt->bindParam(':release_order_number', $release_order_number);
        }

        $stmt->bindParam(':release_date', $release_date);
        $stmt->bindParam(':release_conditions', $release_conditions);
        $stmt->bindParam(':next_court_date', $next_court_date);
        $stmt->bindParam(':court_location', $court_location);
        $stmt->bindParam(':remarks', $remarks);
        $stmt->bindParam(':release_status', $release_status);
        $stmt->bindParam(':processed_by', $processed_by);
        $stmt->bindParam(':processed_at', $processed_at);
        $stmt->bindParam(':approved_by', $approved_by);
        $stmt->bindParam(':approved_at', $approved_at);
        
        if ($stmt->execute()) {
            // Update blotter status based on release status
            $blotter_status = ($release_status == 'Released') ? 'On Bail' : 'Active';
            $status_update = $conn->prepare("UPDATE blotter_entries SET status = :status WHERE blotter_id = :blotter_id");
            $status_update->bindParam(':status', $blotter_status);
            $status_update->bindParam(':blotter_id', $blotter_id);
            $status_update->execute();
            
            // If schedule court hearing option is selected
            if (isset($_POST['schedule_hearing']) && $_POST['schedule_hearing'] == '1') {
                // Insert court hearing
                $sql = "INSERT INTO court_hearings (
                        blotter_id, hearing_date, hearing_type, hearing_location, 
                        attendance_required, hearing_details, recorded_by, recorded_at
                    ) VALUES (
                        :blotter_id, :hearing_date, 'Initial Appearance', :hearing_location, 
                        1, 'Initial court appearance after release on bail', :recorded_by, :recorded_at
                    )";
                $stmt = $conn->prepare($sql);
                $stmt->bindParam(':blotter_id', $blotter_id);
                $stmt->bindParam(':hearing_date', $next_court_date);
                $stmt->bindParam(':hearing_location', $court_location);
                $stmt->bindParam(':recorded_by', $processed_by);
                $stmt->bindParam(':recorded_at', $processed_at);
                $stmt->execute();
            }
            
            $_SESSION['success'] = 'Release order ' . ($blotter['release_id'] ? 'updated' : 'created') . ' successfully.';
            
            // Redirect using JavaScript instead of headers
            if (isset($_POST['print_release_order']) && $_POST['print_release_order'] == '1') {
                echo "<script>window.location.href='print_release_order.php?id=" . $blotter_id . "';</script>";
                exit();
            } else {
                echo "<script>window.location.href='view_case.php?id=" . $blotter_id . "';</script>";
                exit();
            }
        } else {
            $_SESSION['error'] = 'Failed to ' . ($blotter['release_id'] ? 'update' : 'create') . ' release order.';
        }
    } catch (PDOException $e) {
        $_SESSION['error'] = 'Database error: ' . $e->getMessage();
    }
}
?>

<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="mt-4">Release Order Processing</h1>
            <ol class="breadcrumb mb-4">
                <li class="breadcrumb-item"><a href="../../index.php">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="index.php">Bail Management</a></li>
                <?php if ($list_mode): ?>
                    <li class="breadcrumb-item active">Release Order Processing</li>
                <?php else: ?>
                    <li class="breadcrumb-item"><a href="blotter.php">Blotter Entries</a></li>
                    <li class="breadcrumb-item active">Release Order</li>
                <?php endif; ?>
            </ol>

            <?php if ($list_mode): ?>
                <!-- List of Cases Eligible for Release Order -->
                <div class="card mb-4">
                    <div class="card-header bg-warning text-white">
                        <h5 class="mb-0">
                            📄 Cases Eligible for Release Order
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Ref #</th>
                                        <th>Suspect</th>
                                        <th>Case Type</th>
                                        <th>Status</th>
                                        <th>Bail Amount</th>
                                        <th>Payment Status</th>
                                        <th>Release Order Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($release_cases)): ?>
                                        <tr>
                                            <td colspan="8" class="text-center">No cases eligible for release orders found.</td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($release_cases as $case): ?>
                                            <?php 
                                                $total_paid = $case['total_paid'] ?: 0;
                                                $payment_status = $total_paid >= $case['bail_amount'] ? 'Paid' : 'Partial';
                                                $release_status = $case['has_release_order'] > 0 ? 'Created' : 'Pending';
                                            ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($case['reference_number']); ?></td>
                                                <td><?php echo htmlspecialchars($case['suspect_name']); ?></td>
                                                <td><?php echo htmlspecialchars($case['case_type']); ?></td>
                                                <td><?php echo getBadgeHtml($case['status']); ?></td>
                                                <td><?php echo formatCurrency($case['bail_amount']); ?></td>
                                                <td><?php echo getBadgeHtml($payment_status); ?></td>
                                                <td><?php echo getBadgeHtml($release_status); ?></td>
                                                <td>
                                                    <a href="release_order.php?id=<?php echo $case['blotter_id']; ?>" class="btn btn-sm btn-primary">
                                                        📄 Process Order
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="row">
                    <div class="col-xl-4">
                        <!-- Case Information -->
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    📋 Case Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-group mb-3">
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Reference Number:</span>
                                        <strong>🔢 <?php echo htmlspecialchars($blotter['reference_number']); ?></strong>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Suspect Name:</span>
                                        <strong>🧑 <?php echo htmlspecialchars($blotter['suspect_name']); ?></strong>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Case Type:</span>
                                        <strong>📂 <?php echo htmlspecialchars($blotter['case_type']); ?></strong>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Status:</span>
                                        <strong>
                                            <?php 
                                            $status_class = '';
                                            switch ($blotter['status']) {
                                                case 'Active':
                                                    $status_class = 'bg-success';
                                                    break;
                                                case 'On Bail':
                                                    $status_class = 'bg-success';
                                                    break;
                                                default:
                                                    $status_class = 'bg-secondary';
                                            }
                                            ?>
                                            <span class="badge <?php echo $status_class; ?>"><?php echo htmlspecialchars($blotter['status']); ?></span>
                                        </strong>
                                    </li>
                                </ul>
                                
                                <div class="card bg-light mb-3">
                                    <div class="card-body">
                                        <h6 class="card-title">Bail Summary</h6>
                                        <ul class="list-unstyled">
                                            <li><strong>Bail Amount:</strong> 💰 ₱<?php echo number_format($bail_amount, 2); ?></li>
                                            <li><strong>Amount Paid:</strong> 💵 ₱<?php echo number_format($total_paid, 2); ?></li>
                                            <li>
                                                <strong>Payment Status:</strong> 
                                                <span class="badge bg-success">✅ Fully Paid</span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <div class="text-center">
                                    <a href="view_case.php?id=<?php echo $blotter_id; ?>" class="btn btn-info btn-sm">
                                        👁️ View Full Case Details
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Release Status Info -->
                        <?php if ($release_order): ?>
                            <div class="card mb-4">
                                <div class="card-header bg-secondary text-white">
                                    <h5 class="mb-0">
                                        ℹ️ Current Release Status
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <ul class="list-group">
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>Order Number:</span>
                                            <strong>🔢 <?php echo htmlspecialchars($release_order['release_order_number']); ?></strong>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>Status:</span>
                                            <strong>
                                                <?php 
                                                $status_class = '';
                                                switch ($release_order['release_status']) {
                                                    case 'Pending':
                                                        $status_class = 'bg-warning';
                                                        break;
                                                    case 'Approved':
                                                        $status_class = 'bg-success';
                                                        break;
                                                    case 'Released':
                                                        $status_class = 'bg-success';
                                                        break;
                                                    case 'Cancelled':
                                                        $status_class = 'bg-danger';
                                                        break;
                                                    default:
                                                        $status_class = 'bg-secondary';
                                                }
                                                ?>
                                                <span class="badge <?php echo $status_class; ?>"><?php echo htmlspecialchars($release_order['release_status']); ?></span>
                                            </strong>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>Processed By:</span>
                                            <strong>👤 <?php echo htmlspecialchars($release_order['processed_by_name']); ?></strong>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>Last Updated:</span>
                                            <strong>🕒 <?php echo date('M d, Y h:i A', strtotime($release_order['processed_at'])); ?></strong>
                                        </li>
                                        <?php if (!empty($release_order['approved_by'])): ?>
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>Approved By:</span>
                                            <strong>✅ <?php echo htmlspecialchars($release_order['approved_by_name']); ?></strong>
                                        </li>
                                        <?php endif; ?>
                                        <?php if (!empty($release_order['approved_at'])): ?>
                                        <li class="list-group-item d-flex justify-content-between">
                                            <span>Approval Date:</span>
                                            <strong>📅 <?php echo date('M d, Y h:i A', strtotime($release_order['approved_at'])); ?></strong>
                                        </li>
                                        <?php endif; ?>
                                    </ul>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="col-xl-8">
                        <!-- Release Order Form -->
                        <div class="card mb-4">
                            <div class="card-header bg-secondary text-white">
                                <h5 class="mb-0">
                                    📄 Release Order Form
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="release_order.php?id=<?php echo $blotter_id; ?>" id="releaseOrderForm">
                                    <?php if ($release_order): ?>
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="release_order_number" name="release_order_number" value="<?php echo htmlspecialchars($release_order['release_order_number']); ?>" readonly>
                                            <label for="release_order_number">🔢 Release Order Number</label>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="date" class="form-control" id="release_date" name="release_date" value="<?php echo $release_order ? $release_order['release_date'] : date('Y-m-d'); ?>" required>
                                                <label for="release_date">📅 Release Date</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <select class="form-select" id="release_status" name="release_status" required>
                                                    <option value="">Select status</option>
                                                    <option value="Pending" <?php echo ($release_order && $release_order['release_status'] == 'Pending') ? 'selected' : ''; ?>>⏳ Pending</option>
                                                    <option value="Approved" <?php echo ($release_order && $release_order['release_status'] == 'Approved') ? 'selected' : ''; ?>>✅ Approved</option>
                                                    <option value="Released" <?php echo ($release_order && $release_order['release_status'] == 'Released') ? 'selected' : ''; ?>>🔓 Released</option>
                                                    <option value="Cancelled" <?php echo ($release_order && $release_order['release_status'] == 'Cancelled') ? 'selected' : ''; ?>>❌ Cancelled</option>
                                                </select>
                                                <label for="release_status">🚦 Release Status</label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="datetime-local" class="form-control" id="next_court_date" name="next_court_date" value="<?php echo $release_order ? str_replace(' ', 'T', $release_order['next_court_date']) : ''; ?>" required>
                                                <label for="next_court_date">📅 Next Court Date</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-floating mb-3">
                                                <input type="text" class="form-control" id="court_location" name="court_location" placeholder="Court Location" value="<?php echo $release_order ? htmlspecialchars($release_order['court_location']) : ''; ?>" required>
                                                <label for="court_location">🏛️ Court Location</label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="form-floating mb-3">
                                        <textarea class="form-control" id="release_conditions" name="release_conditions" placeholder="Release Conditions" style="height: 120px" required><?php echo $release_order ? htmlspecialchars($release_order['release_conditions']) : "1. Appear at all court proceedings as required.\n2. Do not leave the jurisdiction without court permission.\n3. Do not communicate with any alleged victim or witness.\n4. Report to the Barangay office weekly.\n5. Surrender any travel documents."; ?></textarea>
                                        <label for="release_conditions">📝 Release Conditions</label>
                                    </div>
                                    
                                    <div class="form-floating mb-3">
                                        <textarea class="form-control" id="remarks" name="remarks" placeholder="Remarks" style="height: 100px"><?php echo $release_order ? htmlspecialchars($release_order['remarks']) : ''; ?></textarea>
                                        <label for="remarks">📝 Additional Remarks</label>
                                    </div>
                                    
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="schedule_hearing" name="schedule_hearing" value="1" <?php echo (!$release_order) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="schedule_hearing">
                                            📅 Automatically schedule court hearing for the specified date
                                        </label>
                                    </div>
                                    
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="print_release_order" name="print_release_order" value="1">
                                        <label class="form-check-label" for="print_release_order">
                                            🖨️ Print release order after saving
                                        </label>
                                    </div>
                                    
                                    <div class="alert alert-info mb-3">
                                        ℹ️ This is an important legal document that authorizes the release of the suspect from detention.
                                    </div>
                                    
                                    <div class="d-flex justify-content-between">
                                        <a href="bail_payment.php?id=<?php echo $blotter_id; ?>" class="btn btn-secondary">↩️ Back to Payment</a>
                                        <button type="submit" name="save_order" class="btn btn-primary">
                                            💾 <?php echo $release_order ? 'Update' : 'Create'; ?> Release Order
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </main>
    </div>
</div>

<?php include '../../includes/footer.php'; ?>

<!-- Toast container -->
<div class="toast-container position-fixed top-0 end-0 p-3">
    <div id="successToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex align-items-center p-3">
            <div class="me-2">✅</div>
            <div class="me-auto"><?php echo isset($_SESSION['success']) ? $_SESSION['success'] : (isset($_SESSION['error']) ? $_SESSION['error'] : ''); ?></div>
            <div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set default next court date if creating new release order
        <?php if (!$release_order): ?>
            const nextMonth = new Date();
            nextMonth.setDate(nextMonth.getDate() + 30);
            document.getElementById('next_court_date').value = nextMonth.toISOString().slice(0, 16);
            document.getElementById('release_status').value = 'Pending';
        <?php endif; ?>
        
        // Form validation
        const form = document.getElementById('releaseOrderForm');
        form.addEventListener('submit', function(event) {
            const releaseStatus = document.getElementById('release_status').value;
            const nextCourtDate = document.getElementById('next_court_date').value;
            
            if (releaseStatus === '') {
                event.preventDefault();
                alert('Please select a release status.');
                document.getElementById('release_status').focus();
                return;
            }
            
            if (nextCourtDate === '') {
                event.preventDefault();
                alert('Please enter the next court date.');
                document.getElementById('next_court_date').focus();
            }
        });

        // Show toast notification if there's a message
        <?php if(isset($_SESSION['success']) || isset($_SESSION['error'])): ?>
        // Update toast styling based on message type
        const toast = document.getElementById('successToast');
        
        <?php if(isset($_SESSION['success'])): ?>
        toast.style.backgroundColor = '#28a745'; // success green
        toast.style.color = 'white';
        <?php elseif(isset($_SESSION['error'])): ?>
        toast.style.backgroundColor = '#dc3545'; // danger red
        toast.style.color = 'white';
        <?php endif; ?>
        
        const successToast = new bootstrap.Toast(toast, {
            delay: 5000
        });
        successToast.show();

        <?php 
        // Clear session messages after displaying
        if(isset($_SESSION['success'])) unset($_SESSION['success']);
        if(isset($_SESSION['error'])) unset($_SESSION['error']);
        ?>
        <?php endif; ?>
    });
</script> 