<?php
// Include session and core files first
include_once '../../includes/session.php';
include_once '../../includes/header.php';
include_once '../../includes/sidebar.php';

// Now include the bail module header (it will include permission functions)
include_once 'bail_header.php';

// Set page title
$page_title = 'Bail Management Dashboard';

// Count statistics
try {
    // Total blotter entries
    $stmt = $conn->query("SELECT COUNT(*) as total FROM blotter_entries");
    $total_blotter = $stmt->fetch()['total'] ?? 0;
    
    // Active bail cases
    $stmt = $conn->query("SELECT COUNT(*) as total FROM blotter_entries WHERE status = 'On Bail'");
    $active_bail = $stmt->fetch()['total'] ?? 0;
    
    // Pending bail assessment
    $stmt = $conn->query("
        SELECT COUNT(*) as total FROM blotter_entries be
        LEFT JOIN bail_assessments ba ON be.blotter_id = ba.blotter_id
        WHERE be.status = 'Active' AND (ba.assessment_id IS NULL OR ba.is_bailable IS NULL)
    ");
    $pending_assessment = $stmt->fetch()['total'] ?? 0;
    
    // Upcoming hearings
    $stmt = $conn->query("
        SELECT COUNT(*) as total FROM court_hearings
        WHERE hearing_date > NOW() AND attendance_required = 1
    ");
    $upcoming_hearings = $stmt->fetch()['total'] ?? 0;
    
    // Bail violations
    $stmt = $conn->query("SELECT COUNT(*) as total FROM bail_violations WHERE status IN ('Reported', 'In Process')");
    $bail_violations = $stmt->fetch()['total'] ?? 0;
    
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error: ' . $e->getMessage();
    $total_blotter = $active_bail = $pending_assessment = $upcoming_hearings = $bail_violations = 0;
}

// Get recent blotter entries with bail status
try {
    $stmt = $conn->query("
        SELECT be.*, ba.is_bailable, ba.bail_amount, 
               (SELECT COUNT(*) FROM court_hearings ch WHERE ch.blotter_id = be.blotter_id AND ch.hearing_date > NOW()) as upcoming_hearings
        FROM blotter_entries be
        LEFT JOIN bail_assessments ba ON be.blotter_id = ba.blotter_id
        ORDER BY be.created_at DESC
        LIMIT 10
    ");
    $recent_cases = $stmt->fetchAll();
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error: ' . $e->getMessage();
    $recent_cases = [];
}

// Get upcoming hearings
try {
    $stmt = $conn->query("
        SELECT ch.*, be.reference_number, be.suspect_name, be.case_type
        FROM court_hearings ch
        JOIN blotter_entries be ON ch.blotter_id = be.blotter_id
        WHERE ch.hearing_date > NOW()
        ORDER BY ch.hearing_date ASC
        LIMIT 5
    ");
    $upcoming_hearing_list = $stmt->fetchAll();
} catch (PDOException $e) {
    $_SESSION['error'] = 'Database error: ' . $e->getMessage();
    $upcoming_hearing_list = [];
}
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar is included above -->
        
        <!-- Main Content -->
        <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="mt-4">👮‍♂️ Bail Management Dashboard</h1>
            <ol class="breadcrumb mb-4">
                <li class="breadcrumb-item"><a href="dashboard.php">Bail Dashboard</a></li>
                <li class="breadcrumb-item active">Bail Management</li>
            </ol>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stat-card shadow h-100">
                        <div class="card-body p-3">
                            <div class="row align-items-center">
                                <div class="col-4">
                                    <div class="stat-icon bg-primary-soft text-primary">
                                        📋
                                    </div>
                                </div>
                                <div class="col-8 text-end">
                                    <h4 class="mt-0 mb-1"><?php echo $total_blotter; ?></h4>
                                    <p class="mb-0 text-muted">Total Blotter Entries</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stat-card shadow h-100">
                        <div class="card-body p-3">
                            <div class="row align-items-center">
                                <div class="col-4">
                                    <div class="stat-icon bg-success-soft text-success">
                                        ⚖️
                                    </div>
                                </div>
                                <div class="col-8 text-end">
                                    <h4 class="mt-0 mb-1"><?php echo $active_bail; ?></h4>
                                    <p class="mb-0 text-muted">Active Bail Cases</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stat-card shadow h-100">
                        <div class="card-body p-3">
                            <div class="row align-items-center">
                                <div class="col-4">
                                    <div class="stat-icon bg-warning-soft text-warning">
                                        ⏳
                                    </div>
                                </div>
                                <div class="col-8 text-end">
                                    <h4 class="mt-0 mb-1"><?php echo $pending_assessment; ?></h4>
                                    <p class="mb-0 text-muted">Pending Assessments</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stat-card shadow h-100">
                        <div class="card-body p-3">
                            <div class="row align-items-center">
                                <div class="col-4">
                                    <div class="stat-icon bg-danger-soft text-danger">
                                        📅
                                    </div>
                                </div>
                                <div class="col-8 text-end">
                                    <h4 class="mt-0 mb-1"><?php echo $upcoming_hearings; ?></h4>
                                    <p class="mb-0 text-muted">Upcoming Hearings</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Workflow Navigation -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card stat-card shadow">
                        <div class="card-header bg-transparent">
                            <h5 class="mb-0">🔄 Bail Procedure Workflow</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-2 mb-3">
                                    <a href="blotter.php" class="text-decoration-none">
                                        <div class="p-3 bg-light rounded workflow-step shadow-sm">
                                            <div class="mb-2">
                                                <div class="stat-icon bg-primary-soft text-primary mx-auto">
                                                    📋
                                                </div>
                                            </div>
                                            <h5>Step 1</h5>
                                            <p class="mb-0">Arrest & Blotter Entry</p>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <a href="bail_assessment.php" class="text-decoration-none">
                                        <div class="p-3 bg-light rounded workflow-step shadow-sm">
                                            <div class="mb-2">
                                                <div class="stat-icon bg-success-soft text-success mx-auto">
                                                    ⚖️
                                                </div>
                                            </div>
                                            <h5>Step 2</h5>
                                            <p class="mb-0">Case Filing & Bail Assessment</p>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <a href="bail_payment.php" class="text-decoration-none">
                                        <div class="p-3 bg-light rounded workflow-step shadow-sm">
                                            <div class="mb-2">
                                                <div class="stat-icon bg-info-soft text-info mx-auto">
                                                    💰
                                                </div>
                                            </div>
                                            <h5>Step 3</h5>
                                            <p class="mb-0">Bail Payment & Processing</p>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <a href="release_order.php" class="text-decoration-none">
                                        <div class="p-3 bg-light rounded workflow-step shadow-sm">
                                            <div class="mb-2">
                                                <div class="stat-icon bg-warning-soft text-warning mx-auto">
                                                    📄
                                                </div>
                                            </div>
                                            <h5>Step 4</h5>
                                            <p class="mb-0">Release Order Processing</p>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <a href="hearings.php" class="text-decoration-none">
                                        <div class="p-3 bg-light rounded workflow-step shadow-sm">
                                            <div class="mb-2">
                                                <div class="stat-icon bg-danger-soft text-danger mx-auto">
                                                    🔨
                                                </div>
                                            </div>
                                            <h5>Step 5</h5>
                                            <p class="mb-0">Monitoring & Compliance</p>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <a href="reports.php" class="text-decoration-none">
                                        <div class="p-3 bg-light rounded workflow-step shadow-sm">
                                            <div class="mb-2">
                                                <div class="stat-icon bg-secondary-soft text-secondary mx-auto">
                                                    📊
                                                </div>
                                            </div>
                                            <h5>Reports</h5>
                                            <p class="mb-0">Bail System Reports</p>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Cases and Upcoming Hearings -->
            <div class="row">
                <!-- Recent Blotter Cases -->
                <div class="col-lg-7">
                    <div class="card stat-card shadow mb-4">
                        <div class="card-header bg-transparent">
                            <h5 class="mb-0">📋 Recent Blotter Entries</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($recent_cases)): ?>
                                <div class="text-center py-3">
                                    <p class="mb-0">No recent cases found</p>
                                </div>
                            <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="bail-recent-cases">
                                    <thead>
                                        <tr>
                                            <th>🔢 Ref #</th>
                                            <th>👨‍⚖️ Suspect</th>
                                            <th>📝 Case Type</th>
                                            <th>🚦 Status</th>
                                            <th>💵 Bail Amount</th>
                                            <th>⚙️ Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_cases as $case): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($case['reference_number']); ?></td>
                                                <td><?php echo htmlspecialchars($case['suspect_name']); ?></td>
                                                <td><?php echo htmlspecialchars($case['case_type']); ?></td>
                                                <td><?php echo getBadgeHtml($case['status']); ?></td>
                                                <td>
                                                    <?php if (isset($case['is_bailable']) && $case['is_bailable'] == 1): ?>
                                                        <?php echo formatCurrency($case['bail_amount']); ?>
                                                    <?php elseif (isset($case['is_bailable']) && $case['is_bailable'] == 0): ?>
                                                        <?php echo getBadgeHtml('Non-Bailable'); ?>
                                                    <?php else: ?>
                                                        <?php echo getBadgeHtml('Pending'); ?>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <a href="view_case.php?id=<?php echo $case['blotter_id']; ?>" class="btn btn-sm btn-info me-1">
                                                        👁️ View
                                                    </a>
                                                    <div class="dropdown d-inline-block">
                                                        <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                            <i class="fas fa-cog"></i>
                                                        </button>
                                                        <ul class="dropdown-menu">
                                                            <li><a class="dropdown-item" href="view_case.php?id=<?php echo $case['blotter_id']; ?>"><i class="fas fa-eye me-2"></i>👁️ View Details</a></li>
                                                            <li><a class="dropdown-item" href="bail_assessment.php?id=<?php echo $case['blotter_id']; ?>"><i class="fas fa-balance-scale me-2"></i>⚖️ Assess Bail</a></li>
                                                            <li><a class="dropdown-item" href="bail_payment.php?id=<?php echo $case['blotter_id']; ?>"><i class="fas fa-money-bill-wave me-2"></i>💰 Process Payment</a></li>
                                                            <li><a class="dropdown-item" href="release_order.php?id=<?php echo $case['blotter_id']; ?>"><i class="fas fa-file-alt me-2"></i>📄 Release Order</a></li>
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li><a class="dropdown-item" href="schedule_hearing.php?id=<?php echo $case['blotter_id']; ?>"><i class="fas fa-calendar-plus me-2"></i>📅 Schedule Hearing</a></li>
                                                            <li><a class="dropdown-item" href="hearings.php?blotter_id=<?php echo $case['blotter_id']; ?>"><i class="fas fa-gavel me-2"></i>🔨 View Hearings</a></li>
                                                            <li><a class="dropdown-item" href="bail_violations.php?blotter_id=<?php echo $case['blotter_id']; ?>"><i class="fas fa-exclamation-triangle me-2"></i>⚠️ Manage Violations</a></li>
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php endif; ?>
                        </div>
                        <div class="card-footer bg-transparent d-flex justify-content-between">
                            <a href="blotter.php" class="btn btn-sm btn-primary">
                                🔍 View All Cases
                            </a>
                            <a href="new_blotter.php" class="btn btn-sm btn-success">
                                ➕ Add New Case
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Upcoming Hearings -->
                <div class="col-lg-5">
                    <div class="card stat-card shadow mb-4">
                        <div class="card-header bg-transparent">
                            <h5 class="mb-0">📅 Upcoming Court Hearings</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($upcoming_hearing_list)): ?>
                                <div class="text-center py-3">
                                    <p class="mb-0">No upcoming hearings scheduled</p>
                                </div>
                            <?php else: ?>
                                <div class="list-group shadow-sm">
                                    <?php foreach ($upcoming_hearing_list as $hearing): ?>
                                        <a href="view_hearing.php?id=<?php echo $hearing['hearing_id']; ?>" class="list-group-item list-group-item-action border-0 mb-2 rounded">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h5 class="mb-1"><?php echo htmlspecialchars($hearing['suspect_name']); ?></h5>
                                                <span class="badge bg-primary rounded-pill">
                                                    <?php echo date('M d, Y', strtotime($hearing['hearing_date'])); ?>
                                                </span>
                                            </div>
                                            <p class="mb-1">
                                                <strong>Case:</strong> <?php echo htmlspecialchars($hearing['case_type']); ?><br>
                                                <strong>Time:</strong> <?php echo date('h:i A', strtotime($hearing['hearing_date'])); ?><br>
                                                <strong>Location:</strong> <?php echo htmlspecialchars($hearing['hearing_location']); ?>
                                            </p>
                                            <small class="text-muted">Ref #: <?php echo htmlspecialchars($hearing['reference_number']); ?></small>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="card-footer bg-transparent d-flex justify-content-between">
                            <a href="hearings.php" class="btn btn-sm btn-primary">
                                🔍 View All Hearings
                            </a>
                            <a href="schedule_hearing.php" class="btn btn-sm btn-success">
                                ➕ Schedule New Hearing
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../../includes/footer.php'; ?>

<!-- Toast container -->
<div class="toast-container position-fixed top-0 end-0 p-3">
    <div id="successToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex align-items-center p-3">
            <div class="me-2">✅</div>
            <div class="me-auto"><?php echo isset($_SESSION['success']) ? $_SESSION['success'] : (isset($_SESSION['error']) ? $_SESSION['error'] : ''); ?></div>
            <div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Initialize DataTables for recent cases
        if ($('#bail-recent-cases').length > 0) {
            $('#bail-recent-cases').DataTable({
                "responsive": true,
                "lengthMenu": [[5, 10, 25, -1], [5, 10, 25, "All"]],
                "pageLength": 5,
                "order": [[0, "desc"]],
                "columnDefs": [
                    { "orderable": false, "targets": 5 } // Disable sorting on action column
                ]
            });
        }
        
        // Add hover effect to workflow steps
        $('.workflow-step').hover(
            function() {
                $(this).addClass('shadow');
                $(this).css('transform', 'translateY(-5px)');
            },
            function() {
                $(this).removeClass('shadow');
                $(this).css('transform', 'translateY(0)');
            }
        );
        
        // Add contextual hover tooltips to workflow steps
        $('.workflow-step').tooltip({
            title: function() {
                let step = $(this).find('p.mb-0').text();
                switch(step) {
                    case 'Arrest & Blotter Entry':
                        return 'Create and manage blotter entries for arrests';
                    case 'Case Filing & Bail Assessment':
                        return 'Assess bail eligibility and set bail amounts';
                    case 'Bail Payment & Processing':
                        return 'Record and process bail payments';
                    case 'Release Order Processing':
                        return 'Generate and manage release orders';
                    case 'Monitoring & Compliance':
                        return 'Schedule hearings and monitor compliance';
                    default:
                        return 'View system reports and statistics';
                }
            },
            placement: 'top'
        });

        // Show toast notification if there's a message
        <?php if(isset($_SESSION['success']) || isset($_SESSION['error'])): ?>
        // Update toast styling based on message type
        const toast = document.getElementById('successToast');
        
        <?php if(isset($_SESSION['success'])): ?>
        toast.style.backgroundColor = '#28a745'; // success green
        toast.style.color = 'white';
        <?php elseif(isset($_SESSION['error'])): ?>
        toast.style.backgroundColor = '#dc3545'; // danger red
        toast.style.color = 'white';
        <?php endif; ?>
        
        const successToast = new bootstrap.Toast(toast, {
            delay: 5000
        });
        successToast.show();

        <?php 
        // Clear session messages after displaying
        if(isset($_SESSION['success'])) unset($_SESSION['success']);
        if(isset($_SESSION['error'])) unset($_SESSION['error']);
        ?>
        <?php endif; ?>
    });
</script> 