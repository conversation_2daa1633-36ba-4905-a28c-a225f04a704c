<?php
// Include database connection
include '../../includes/session.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include permission functions
include '../../includes/functions/permission_functions.php';

// Check if user has permission to access bail module
if (!canAccessModule('bail')) {
    $_SESSION['error'] = 'You do not have permission to access this page.';
    header('Location: ../../index.php');
    exit();
}

// Set page title
$page_title = 'Schedule Court Hearing';

// Check if a blotter ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    // Instead of redirecting, display a case selection dropdown
    $display_case_selection = true;
    
    // Fetch active cases for dropdown
    try {
        $stmt = $conn->query("
            SELECT be.blotter_id, be.reference_number, be.suspect_name, be.case_type, be.status
            FROM blotter_entries be
            WHERE be.status IN ('Active', 'On Bail')
            ORDER BY be.created_at DESC
        ");
        $available_cases = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($available_cases)) {
            $_SESSION['error'] = 'No active cases available to schedule a hearing.';
            header('Location: blotter.php');
            exit();
        }
    } catch (PDOException $e) {
        $_SESSION['error'] = 'Database error: ' . $e->getMessage();
        header('Location: blotter.php');
        exit();
    }
    
    // No specific case selected yet
    $case = null;
    $hearings = [];
    $blotter_id = null;
} else {
    $display_case_selection = false;
    $blotter_id = $_GET['id'];
    
    // Fetch case details
    try {
        $stmt = $conn->prepare("
            SELECT be.*, u.username as created_by_name
            FROM blotter_entries be
            LEFT JOIN users u ON be.created_by = u.user_id
            WHERE be.blotter_id = :blotter_id
        ");
        $stmt->bindParam(':blotter_id', $blotter_id);
        $stmt->execute();
        $case = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$case) {
            $_SESSION['error'] = 'Case not found.';
            header('Location: blotter.php');
            exit();
        }

        // Get existing hearings
        $stmt = $conn->prepare("
            SELECT ch.*, u.username as recorded_by_name
            FROM court_hearings ch
            LEFT JOIN users u ON ch.recorded_by = u.user_id
            WHERE ch.blotter_id = :blotter_id
            ORDER BY ch.hearing_date ASC
        ");
        $stmt->bindParam(':blotter_id', $blotter_id);
        $stmt->execute();
        $hearings = $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (PDOException $e) {
        $_SESSION['error'] = 'Database error: ' . $e->getMessage();
        header('Location: blotter.php');
        exit();
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // If case was selected from dropdown
        if (isset($_POST['selected_case'])) {
            $blotter_id = $_POST['selected_case'];
        } else {
            // Ensure blotter_id is set
            $blotter_id = $_POST['blotter_id'] ?? null;
            
            if (!$blotter_id) {
                throw new Exception('No case selected.');
            }
        }
        
        $hearing_date = $_POST['hearing_date'];
        $hearing_type = $_POST['hearing_type'];
        $hearing_location = $_POST['hearing_location'];
        $attendance_required = isset($_POST['attendance_required']) ? 1 : 0;
        $recorded_by = $_SESSION['user_id'];
        $recorded_at = date('Y-m-d H:i:s');

        // Insert court hearing
        $sql = "INSERT INTO court_hearings (
                blotter_id, hearing_date, hearing_type, hearing_location, 
                attendance_required, recorded_by, recorded_at
            ) VALUES (
                :blotter_id, :hearing_date, :hearing_type, :hearing_location, 
                :attendance_required, :recorded_by, :recorded_at
            )";
        
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':blotter_id', $blotter_id);
        $stmt->bindParam(':hearing_date', $hearing_date);
        $stmt->bindParam(':hearing_type', $hearing_type);
        $stmt->bindParam(':hearing_location', $hearing_location);
        $stmt->bindParam(':attendance_required', $attendance_required);
        $stmt->bindParam(':recorded_by', $recorded_by);
        $stmt->bindParam(':recorded_at', $recorded_at);
        
        if ($stmt->execute()) {
            $_SESSION['success'] = 'Court hearing scheduled successfully.';
            
            // Redirect to hearings page if requested
            if (isset($_POST['return_to_hearings']) && $_POST['return_to_hearings'] == '1') {
                header('Location: hearings.php?blotter_id=' . $blotter_id);
            } else {
                header('Location: view_case.php?id=' . $blotter_id);
            }
            exit();
        } else {
            $_SESSION['error'] = 'Failed to schedule court hearing.';
        }
    } catch (PDOException $e) {
        $_SESSION['error'] = 'Database error: ' . $e->getMessage();
    } catch (Exception $e) {
        $_SESSION['error'] = $e->getMessage();
    }
}

// Include header and sidebar
include '../../includes/header.php';
include '../../includes/sidebar.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="mt-4">Schedule Court Hearing</h1>
            <ol class="breadcrumb mb-4">
                <li class="breadcrumb-item"><a href="../../index.php">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="index.php">Bail Management</a></li>
                <li class="breadcrumb-item"><a href="hearings.php">Court Hearings</a></li>
                <li class="breadcrumb-item active">Schedule Hearing</li>
            </ol>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php 
                    echo $_SESSION['success']; 
                    unset($_SESSION['success']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php 
                    echo $_SESSION['error']; 
                    unset($_SESSION['error']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if ($display_case_selection): ?>
            <!-- Case Selection Card -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-search me-1"></i>
                        Select Case for Hearing
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="case-selection-table">
                            <thead>
                                <tr>
                                    <th>Reference #</th>
                                    <th>Suspect Name</th>
                                    <th>Case Type</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($available_cases as $available_case): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($available_case['reference_number']); ?></td>
                                    <td><?php echo htmlspecialchars($available_case['suspect_name']); ?></td>
                                    <td><?php echo htmlspecialchars($available_case['case_type']); ?></td>
                                    <td><?php echo getStatusBadge($available_case['status']); ?></td>
                                    <td>
                                        <a href="schedule_hearing.php?id=<?php echo $available_case['blotter_id']; ?>" class="btn btn-sm btn-primary">
                                            <i class="fas fa-calendar-plus me-1"></i> Schedule
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-plus me-1"></i>
                        Quick Schedule
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="schedule_hearing.php">
                        <div class="mb-3">
                            <label for="selected_case" class="form-label">Select Case</label>
                            <select class="form-select" id="selected_case" name="selected_case" required>
                                <option value="">-- Select Case --</option>
                                <?php foreach ($available_cases as $available_case): ?>
                                <option value="<?php echo $available_case['blotter_id']; ?>">
                                    <?php echo htmlspecialchars($available_case['reference_number']); ?> - 
                                    <?php echo htmlspecialchars($available_case['suspect_name']); ?> - 
                                    <?php echo htmlspecialchars($available_case['case_type']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="hearing_date" class="form-label">Hearing Date & Time</label>
                            <input type="datetime-local" class="form-control" id="hearing_date" name="hearing_date" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="hearing_type" class="form-label">Hearing Type</label>
                            <select class="form-select" id="hearing_type" name="hearing_type" required>
                                <option value="Initial Appearance">Initial Appearance</option>
                                <option value="Arraignment">Arraignment</option>
                                <option value="Preliminary Hearing">Preliminary Hearing</option>
                                <option value="Pre-Trial Conference">Pre-Trial Conference</option>
                                <option value="Trial">Trial</option>
                                <option value="Sentencing">Sentencing</option>
                                <option value="Appeal">Appeal</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="hearing_location" class="form-label">Hearing Location</label>
                            <input type="text" class="form-control" id="hearing_location" name="hearing_location" required>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="attendance_required" name="attendance_required" checked>
                            <label class="form-check-label" for="attendance_required">
                                Attendance Required
                            </label>
                        </div>
                        
                        <div class="mb-3">
                            <label for="remarks" class="form-label">Remarks/Details</label>
                            <textarea class="form-control" id="remarks" name="remarks" rows="3"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="return_to_hearings" name="return_to_hearings" value="1">
                                <label class="form-check-label" for="return_to_hearings">
                                    Return to Hearings page after scheduling
                                </label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Schedule Hearing</button>
                        <a href="hearings.php" class="btn btn-secondary">Cancel</a>
                    </form>
                </div>
            </div>
            <?php else: ?>
            <!-- Case-specific UI - Keep existing code for when a specific case is selected -->
            <div class="row">
                <!-- Case Information -->
                <div class="col-xl-4">
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-clipboard-list me-1"></i>
                                Case Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-group mb-3">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <strong>Reference Number:</strong>
                                    <span><?php echo htmlspecialchars($case['reference_number']); ?></span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <strong>Suspect Name:</strong>
                                    <span><?php echo htmlspecialchars($case['suspect_name']); ?></span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <strong>Case Type:</strong>
                                    <span><?php echo htmlspecialchars($case['case_type']); ?></span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <strong>Status:</strong>
                                    <span>
                                        <?php 
                                        $status_class = '';
                                        switch ($case['status']) {
                                            case 'Active':
                                                $status_class = 'bg-success';
                                                break;
                                            case 'On Bail':
                                                $status_class = 'bg-success';
                                                break;
                                            case 'Closed':
                                                $status_class = 'bg-secondary';
                                                break;
                                            case 'Transferred':
                                                $status_class = 'bg-info';
                                                break;
                                            case 'Dismissed':
                                                $status_class = 'bg-danger';
                                                break;
                                            default:
                                                $status_class = 'bg-secondary';
                                        }
                                        ?>
                                        <span class="badge <?php echo $status_class; ?>"><?php echo htmlspecialchars($case['status']); ?></span>
                                    </span>
                                </li>
                            </ul>
                            
                            <?php if (!empty($hearings)): ?>
                            <h6 class="mt-4">Previous Hearings</h6>
                            <div class="list-group small">
                                <?php foreach ($hearings as $prev_hearing): ?>
                                <a href="view_hearing.php?id=<?php echo $prev_hearing['hearing_id']; ?>" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($prev_hearing['hearing_type']); ?></h6>
                                        <small><?php echo date('M d, Y', strtotime($prev_hearing['hearing_date'])); ?></small>
                                    </div>
                                    <small><?php echo date('h:i A', strtotime($prev_hearing['hearing_date'])); ?> at <?php echo htmlspecialchars($prev_hearing['hearing_location']); ?></small>
                                </a>
                                <?php endforeach; ?>
                            </div>
                            <?php endif; ?>
                            
                            <div class="mt-3">
                                <a href="view_case.php?id=<?php echo $blotter_id; ?>" class="btn btn-info btn-sm">
                                    <i class="fas fa-eye"></i> View Full Case Details
                                </a>
                                <a href="hearings.php?blotter_id=<?php echo $blotter_id; ?>" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-list"></i> All Hearings
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Schedule Hearing Form -->
                <div class="col-xl-8">
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-calendar-plus me-1"></i>
                                Schedule New Hearing
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="schedule_hearing.php">
                                <input type="hidden" name="blotter_id" value="<?php echo $blotter_id; ?>">
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="hearing_date" class="form-label">Hearing Date & Time</label>
                                        <input type="datetime-local" class="form-control" id="hearing_date" name="hearing_date" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="hearing_type" class="form-label">Hearing Type</label>
                                        <select class="form-select" id="hearing_type" name="hearing_type" required>
                                            <option value="Initial Appearance">Initial Appearance</option>
                                            <option value="Arraignment">Arraignment</option>
                                            <option value="Preliminary Hearing">Preliminary Hearing</option>
                                            <option value="Pre-Trial Conference">Pre-Trial Conference</option>
                                            <option value="Trial">Trial</option>
                                            <option value="Sentencing">Sentencing</option>
                                            <option value="Appeal">Appeal</option>
                                            <option value="Other">Other</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="hearing_location" class="form-label">Hearing Location</label>
                                    <input type="text" class="form-control" id="hearing_location" name="hearing_location" required>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="attendance_required" name="attendance_required" checked>
                                    <label class="form-check-label" for="attendance_required">
                                        Attendance Required
                                    </label>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="remarks" class="form-label">Remarks/Details</label>
                                    <textarea class="form-control" id="remarks" name="remarks" rows="3"></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="return_to_hearings" name="return_to_hearings" value="1">
                                        <label class="form-check-label" for="return_to_hearings">
                                            Return to Hearings page after scheduling
                                        </label>
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">Schedule Hearing</button>
                                <a href="view_case.php?id=<?php echo $blotter_id; ?>" class="btn btn-secondary">Cancel</a>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </main>
    </div>
</div>

<?php include '../../includes/footer.php'; ?>

<script>
    $(document).ready(function() {
        // Initialize DataTables for case selection
        $('#case-selection-table').DataTable({
            "responsive": true,
            "pageLength": 10,
            "order": [[0, "desc"]]
        });
        
        // Set default date-time to tomorrow at 10:00 AM
        var tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(10, 0, 0, 0);
        
        var dateString = tomorrow.toISOString().slice(0, 16);
        $('input[type="datetime-local"]').val(dateString);
        
        // Pre-fill common hearing location based on selection
        $('#hearing_type').change(function() {
            var locationType = $(this).val();
            var locationSuggestions = {
                'Initial Appearance': 'Barangay Hall - Main Conference Room',
                'Arraignment': 'Municipal Court - Room 101',
                'Preliminary Hearing': 'Barangay Hall - Main Conference Room',
                'Pre-Trial Conference': 'Barangay Hall - Justice Office',
                'Trial': 'Municipal Court - Room 102',
                'Sentencing': 'Municipal Court - Room 101',
                'Appeal': 'Regional Trial Court'
            };
            
            if (locationSuggestions[locationType]) {
                $('#hearing_location').val(locationSuggestions[locationType]);
            }
        });
    });
</script> 