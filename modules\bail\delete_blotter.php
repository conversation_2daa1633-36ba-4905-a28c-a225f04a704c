<?php
// Include database connection
include '../../includes/session.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include permission functions
include '../../includes/functions/permission_functions.php';

// Check if user has permission to access bail module
if (!canAccessModule('bail')) {
    $_SESSION['error'] = 'You do not have permission to delete blotter entries.';
    header('Location: blotter.php');
    exit();
}

// Check if blotter ID is provided and it's a POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['blotter_id']) && !empty($_POST['blotter_id'])) {
    $blotter_id = $_POST['blotter_id'];
    
    try {
        // Start transaction
        $conn->beginTransaction();
        
        // Save the reference number for the success message
        $stmt = $conn->prepare("SELECT reference_number FROM blotter_entries WHERE blotter_id = :blotter_id");
        $stmt->bindParam(':blotter_id', $blotter_id);
        $stmt->execute();
        $blotter = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$blotter) {
            $conn->rollBack(); // Roll back if blotter not found
            $_SESSION['error'] = 'Blotter entry not found.';
            header('Location: blotter.php');
            exit();
        }
        
        $reference_number = $blotter['reference_number'];
        
        // Delete related records in the correct order (respecting foreign key constraints)
        
        // 1. Delete bail violations
        $stmt = $conn->prepare("DELETE FROM bail_violations WHERE blotter_id = :blotter_id");
        $stmt->bindParam(':blotter_id', $blotter_id);
        $stmt->execute();
        
        // 2. Delete court hearings
        $stmt = $conn->prepare("DELETE FROM court_hearings WHERE blotter_id = :blotter_id");
        $stmt->bindParam(':blotter_id', $blotter_id);
        $stmt->execute();
        
        // 3. Delete release orders
        $stmt = $conn->prepare("DELETE FROM release_orders WHERE blotter_id = :blotter_id");
        $stmt->bindParam(':blotter_id', $blotter_id);
        $stmt->execute();
        
        // 4. Delete bail payments
        $stmt = $conn->prepare("DELETE FROM bail_payments WHERE blotter_id = :blotter_id");
        $stmt->bindParam(':blotter_id', $blotter_id);
        $stmt->execute();
        
        // 5. Delete bail assessments
        $stmt = $conn->prepare("DELETE FROM bail_assessments WHERE blotter_id = :blotter_id");
        $stmt->bindParam(':blotter_id', $blotter_id);
        $stmt->execute();
        
        // 6. Finally, delete the blotter entry itself
        $stmt = $conn->prepare("DELETE FROM blotter_entries WHERE blotter_id = :blotter_id");
        $stmt->bindParam(':blotter_id', $blotter_id);
        $stmt->execute();
        
        // Commit transaction
        $conn->commit();
        
        // Log the action
        $user_id = $_SESSION['user_id'];
        $action_details = "Deleted blotter entry: " . $reference_number;
        $log_stmt = $conn->prepare("INSERT INTO activity_logs (user_id, action_type, action_details, module, action_timestamp) VALUES (:user_id, 'delete', :action_details, 'Bail Management', NOW())");
        $log_stmt->bindParam(':user_id', $user_id);
        $log_stmt->bindParam(':action_details', $action_details);
        $log_stmt->execute();
        
        $_SESSION['success'] = 'Blotter entry ' . $reference_number . ' has been successfully deleted.';
        
    } catch (PDOException $e) {
        // Rollback transaction on error
        // Check if transaction is active before rolling back
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }
        $_SESSION['error'] = 'Database error: ' . $e->getMessage();
    }
    
} else {
    $_SESSION['error'] = 'Invalid request. Blotter ID is required.';
}

// Redirect back to blotter page
header('Location: blotter.php');
exit();
?> 