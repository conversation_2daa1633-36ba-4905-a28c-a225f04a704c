<?php
// Include database connection
include '../../includes/session.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include permission functions
include_once '../../includes/functions/permission_functions.php';

// Check if user has permission to access bail module
if (!canAccessModule('bail')) {
    $_SESSION['error'] = 'You do not have permission to access the Bail Reports page.';
    header('Location: ../../index.php');
    exit();
}

// Set page title
$page_title = 'Bail Management Reports';

// Handle report parameters
$report_type = isset($_GET['report_type']) ? $_GET['report_type'] : '';
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : date('Y-m-01'); // First day of current month
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : date('Y-m-t'); // Last day of current month
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';

// Initialize report data
$report_data = [];
$report_title = '';
$show_report = false;

// Generate report based on type
if ($_SERVER['REQUEST_METHOD'] === 'GET' && !empty($report_type)) {
    $show_report = true;
    try {
        switch ($report_type) {
            case 'blotter':
                $report_title = 'Blotter Entries Report';
                $sql = "SELECT be.*, 
                        ba.is_bailable, ba.bail_amount,
                        (SELECT COUNT(*) FROM bail_payments bp WHERE bp.blotter_id = be.blotter_id AND bp.payment_status = 'Completed') as payment_count
                       FROM blotter_entries be
                       LEFT JOIN bail_assessments ba ON be.blotter_id = ba.blotter_id
                       WHERE DATE(be.created_at) BETWEEN :date_from AND :date_to";
                
                if (!empty($status_filter) && $status_filter != 'All Statuses') {
                    $sql .= " AND be.status = :status";
                }
                
                $sql .= " ORDER BY be.created_at DESC";
                
                $stmt = $conn->prepare($sql);
                $stmt->bindParam(':date_from', $date_from);
                $stmt->bindParam(':date_to', $date_to);
                
                if (!empty($status_filter) && $status_filter != 'All Statuses') {
                    $stmt->bindParam(':status', $status_filter);
                }
                
                $stmt->execute();
                $report_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
                break;
                
            case 'assessment':
                $report_title = 'Bail Assessments Report';
                $sql = "SELECT ba.*, be.reference_number, be.suspect_name, be.case_type,
                        be.incident_date,
                        u1.username as recommended_by_name,
                        u2.username as approved_by_name
                       FROM bail_assessments ba
                       JOIN blotter_entries be ON ba.blotter_id = be.blotter_id
                       LEFT JOIN users u1 ON ba.recommended_by = u1.user_id
                       LEFT JOIN users u2 ON ba.approved_by = u2.user_id
                       WHERE DATE(ba.assessment_date) BETWEEN :date_from AND :date_to";
                
                if (!empty($status_filter) && $status_filter != 'All Statuses') {
                    $sql .= " AND ba.status = :status";
                }
                
                $sql .= " ORDER BY ba.assessment_date DESC";
                
                $stmt = $conn->prepare($sql);
                $stmt->bindParam(':date_from', $date_from);
                $stmt->bindParam(':date_to', $date_to);
                
                if (!empty($status_filter) && $status_filter != 'All Statuses') {
                    $stmt->bindParam(':status', $status_filter);
                }
                
                $stmt->execute();
                $report_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
                break;
                
            case 'payment':
                $report_title = 'Bail Payments Report';
                $sql = "SELECT bp.*, be.reference_number, be.suspect_name, be.case_type,
                        u.username as received_by_name
                       FROM bail_payments bp
                       JOIN blotter_entries be ON bp.blotter_id = be.blotter_id
                       LEFT JOIN users u ON bp.received_by = u.user_id
                       WHERE DATE(bp.payment_date) BETWEEN :date_from AND :date_to";
                
                if (!empty($status_filter) && $status_filter != 'All Statuses') {
                    $sql .= " AND bp.payment_status = :status";
                }
                
                $sql .= " ORDER BY bp.payment_date DESC";
                
                $stmt = $conn->prepare($sql);
                $stmt->bindParam(':date_from', $date_from);
                $stmt->bindParam(':date_to', $date_to);
                
                if (!empty($status_filter) && $status_filter != 'All Statuses') {
                    $stmt->bindParam(':status', $status_filter);
                }
                
                $stmt->execute();
                $report_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
                break;
                
            case 'release':
                $report_title = 'Release Orders Report';
                $sql = "SELECT ro.*, be.reference_number, be.suspect_name, be.case_type,
                        u1.username as processed_by_name,
                        u2.username as approved_by_name
                       FROM release_orders ro
                       JOIN blotter_entries be ON ro.blotter_id = be.blotter_id
                       LEFT JOIN users u1 ON ro.processed_by = u1.user_id
                       LEFT JOIN users u2 ON ro.approved_by = u2.user_id
                       WHERE DATE(ro.processed_at) BETWEEN :date_from AND :date_to";
                
                if (!empty($status_filter) && $status_filter != 'All Statuses') {
                    $sql .= " AND ro.release_status = :status";
                }
                
                $sql .= " ORDER BY ro.processed_at DESC";
                
                $stmt = $conn->prepare($sql);
                $stmt->bindParam(':date_from', $date_from);
                $stmt->bindParam(':date_to', $date_to);
                
                if (!empty($status_filter) && $status_filter != 'All Statuses') {
                    $stmt->bindParam(':status', $status_filter);
                }
                
                $stmt->execute();
                $report_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
                break;
                
            case 'hearing':
                $report_title = 'Court Hearings Report';
                $sql = "SELECT ch.*, be.reference_number, be.suspect_name, be.case_type,
                        u.username as recorded_by_name
                       FROM court_hearings ch
                       JOIN blotter_entries be ON ch.blotter_id = be.blotter_id
                       LEFT JOIN users u ON ch.recorded_by = u.user_id
                       WHERE DATE(ch.hearing_date) BETWEEN :date_from AND :date_to";
                
                if (!empty($status_filter) && $status_filter != 'All Statuses') {
                    $sql .= " AND ch.status = :status";
                }
                
                $sql .= " ORDER BY ch.hearing_date ASC";
                
                $stmt = $conn->prepare($sql);
                $stmt->bindParam(':date_from', $date_from);
                $stmt->bindParam(':date_to', $date_to);
                
                if (!empty($status_filter) && $status_filter != 'All Statuses') {
                    $stmt->bindParam(':status', $status_filter);
                }
                
                $stmt->execute();
                $report_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
                break;
                
            case 'violation':
                $report_title = 'Bail Violations Report';
                $sql = "SELECT bv.*, be.reference_number, be.suspect_name, be.case_type,
                        u1.username as reported_by_name,
                        u2.username as resolved_by_name
                       FROM bail_violations bv
                       JOIN blotter_entries be ON bv.blotter_id = be.blotter_id
                       LEFT JOIN users u1 ON bv.reported_by = u1.user_id
                       LEFT JOIN users u2 ON bv.resolved_by = u2.user_id
                       WHERE DATE(bv.violation_date) BETWEEN :date_from AND :date_to";
                
                if (!empty($status_filter) && $status_filter != 'All Statuses') {
                    $sql .= " AND bv.status = :status";
                }
                
                $sql .= " ORDER BY bv.violation_date DESC";
                
                $stmt = $conn->prepare($sql);
                $stmt->bindParam(':date_from', $date_from);
                $stmt->bindParam(':date_to', $date_to);
                
                if (!empty($status_filter) && $status_filter != 'All Statuses') {
                    $stmt->bindParam(':status', $status_filter);
                }
                
                $stmt->execute();
                $report_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
                break;
                
            default:
                $_SESSION['error'] = 'Invalid report type.';
                break;
        }
    } catch (PDOException $e) {
        $_SESSION['error'] = 'Database error: ' . $e->getMessage();
        $report_data = [];
    }
}

// Include header and sidebar
include '../../includes/header.php';
include '../../includes/sidebar.php';
include 'bail_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Main Content -->
        <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
            <h1 class="mt-4">Bail Management Reports</h1>
            <ol class="breadcrumb mb-4">
                <li class="breadcrumb-item"><a href="../../index.php">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="index.php">Bail Management</a></li>
                <li class="breadcrumb-item active">Reports</li>
            </ol>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php 
                    echo $_SESSION['success']; 
                    unset($_SESSION['success']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php 
                    echo $_SESSION['error']; 
                    unset($_SESSION['error']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="card mb-4">
                <div class="card-header">
                    📊 Generate Report
                </div>
                <div class="card-body">
                    <form action="reports.php" method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="report_type" class="form-label">Report Type</label>
                                <select class="form-select" id="report_type" name="report_type" required>
                                <option value="" <?php echo empty($report_type) ? 'selected' : ''; ?>>Select Report Type</option>
                                <option value="blotter" <?php echo $report_type == 'blotter' ? 'selected' : ''; ?>>Blotter Entries</option>
                                <option value="assessment" <?php echo $report_type == 'assessment' ? 'selected' : ''; ?>>Bail Assessments</option>
                                <option value="payment" <?php echo $report_type == 'payment' ? 'selected' : ''; ?>>Bail Payments</option>
                                <option value="release" <?php echo $report_type == 'release' ? 'selected' : ''; ?>>Release Orders</option>
                                <option value="hearing" <?php echo $report_type == 'hearing' ? 'selected' : ''; ?>>Court Hearings</option>
                                <option value="violation" <?php echo $report_type == 'violation' ? 'selected' : ''; ?>>Bail Violations</option>
                                </select>
                        </div>
                        <div class="col-md-3">
                            <label for="date_from" class="form-label">Date From</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>" required>
                        </div>
                        <div class="col-md-3">
                            <label for="date_to" class="form-label">Date To</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>" required>
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                <option value="All Statuses" <?php echo $status_filter == 'All Statuses' || empty($status_filter) ? 'selected' : ''; ?>>All Statuses</option>
                                <option value="Active" <?php echo $status_filter == 'Active' ? 'selected' : ''; ?>>Active</option>
                                <option value="Pending" <?php echo $status_filter == 'Pending' ? 'selected' : ''; ?>>Pending</option>
                                <option value="Completed" <?php echo $status_filter == 'Completed' ? 'selected' : ''; ?>>Completed</option>
                                <option value="On Bail" <?php echo $status_filter == 'On Bail' ? 'selected' : ''; ?>>On Bail</option>
                                <option value="Scheduled" <?php echo $status_filter == 'Scheduled' ? 'selected' : ''; ?>>Scheduled</option>
                                <option value="Canceled" <?php echo $status_filter == 'Canceled' ? 'selected' : ''; ?>>Canceled</option>
                                </select>
                        </div>
                        <div class="col-12 mt-3">
                            <button type="submit" class="btn btn-primary">
                                📄 Generate Report
                            </button>
                            <button type="reset" class="btn btn-secondary">
                                🔄 Reset
                            </button>
                            <?php if ($show_report && !empty($report_data)): ?>
                            <button type="button" class="btn btn-success ms-2" onclick="printReport()">
                                🖨️ Print Report
                            </button>
                            <button type="button" class="btn btn-info ms-2" onclick="exportToExcel()">
                                📊 Export to Excel
                            </button>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
            </div>

            <?php if ($show_report): ?>
            <div class="card mb-4" id="reportContent">
                <div class="card-header">
                    📋 <?php echo $report_title; ?>
                </div>
                <div class="card-body">
                    <?php if (empty($report_data)): ?>
                        <div class="alert alert-info">No data found for the selected criteria.</div>
                    <?php else: ?>
                        <?php if ($report_type == 'blotter'): ?>
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Reference #</th>
                                        <th>Suspect Name</th>
                                        <th>Case Type</th>
                                        <th>Bail Amount</th>
                                        <th>Status</th>
                                        <th>Created On</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($report_data as $item): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($item['reference_number']); ?></td>
                                            <td><?php echo htmlspecialchars($item['suspect_name']); ?></td>
                                            <td><?php echo htmlspecialchars($item['case_type']); ?></td>
                                            <td><?php echo isset($item['bail_amount']) ? formatCurrency($item['bail_amount']) : 'Not Set'; ?></td>
                                            <td><?php echo getStatusBadge($item['status']); ?></td>
                                            <td><?php echo date('M d, Y', strtotime($item['created_at'])); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        <?php elseif ($report_type == 'assessment'): ?>
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Case Reference</th>
                                        <th>Suspect Name</th>
                                        <th>Bailable?</th>
                                        <th>Bail Amount</th>
                                        <th>Recommended By</th>
                                        <th>Approved By</th>
                                        <th>Assessment Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($report_data as $item): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($item['reference_number']); ?></td>
                                            <td><?php echo htmlspecialchars($item['suspect_name']); ?></td>
                                            <td><?php echo $item['is_bailable'] ? 'Yes' : 'No'; ?></td>
                                            <td><?php echo $item['is_bailable'] ? formatCurrency($item['bail_amount']) : 'N/A'; ?></td>
                                            <td><?php echo htmlspecialchars($item['recommended_by_name'] ?? 'N/A'); ?></td>
                                            <td><?php echo htmlspecialchars($item['approved_by_name'] ?? 'Pending'); ?></td>
                                            <td><?php echo date('M d, Y', strtotime($item['assessment_date'])); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        <?php elseif ($report_type == 'payment'): ?>
                            <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                        <th>Case Reference</th>
                                        <th>Suspect Name</th>
                                        <th>Amount Paid</th>
                                        <th>Payment Method</th>
                                        <th>Payor Name</th>
                                        <th>Received By</th>
                                        <th>Payment Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                            <?php 
                                    $total_payments = 0;
                                    foreach ($report_data as $item): 
                                        $total_payments += $item['amount'];
                                    ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($item['reference_number']); ?></td>
                                            <td><?php echo htmlspecialchars($item['suspect_name']); ?></td>
                                            <td><?php echo formatCurrency($item['amount']); ?></td>
                                            <td><?php echo htmlspecialchars($item['payment_method']); ?></td>
                                            <td><?php echo isset($item['payer_name']) ? htmlspecialchars($item['payer_name']) : 'N/A'; ?></td>
                                            <td><?php echo htmlspecialchars($item['received_by_name']); ?></td>
                                            <td><?php echo date('M d, Y', strtotime($item['payment_date'])); ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr class="table-dark">
                                        <td colspan="2"><strong>TOTAL</strong></td>
                                        <td colspan="5"><strong><?php echo formatCurrency($total_payments); ?></strong></td>
                                    </tr>
                                </tfoot>
                            </table>
                        <?php elseif ($report_type == 'release'): ?>
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Case Reference</th>
                                        <th>Suspect Name</th>
                                        <th>Status</th>
                                        <th>Release Date</th>
                                        <th>Conditions</th>
                                        <th>Processed By</th>
                                        <th>Approved By</th>
                                </tr>
                            </thead>
                            <tbody>
                                    <?php foreach ($report_data as $item): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($item['reference_number']); ?></td>
                                            <td><?php echo htmlspecialchars($item['suspect_name']); ?></td>
                                            <td><?php echo getStatusBadge($item['release_status']); ?></td>
                                            <td><?php echo isset($item['release_date']) ? date('M d, Y', strtotime($item['release_date'])) : 'Pending'; ?></td>
                                            <td><?php echo htmlspecialchars($item['release_conditions'] ?? 'N/A'); ?></td>
                                            <td><?php echo htmlspecialchars($item['processed_by_name']); ?></td>
                                            <td><?php echo htmlspecialchars($item['approved_by_name'] ?? 'Pending Approval'); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        <?php elseif ($report_type == 'hearing'): ?>
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Case Reference</th>
                                        <th>Suspect Name</th>
                                        <th>Hearing Date</th>
                                        <th>Venue</th>
                                        <th>Status</th>
                                        <th>Outcome</th>
                                        <th>Recorded By</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($report_data as $item): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($item['reference_number']); ?></td>
                                            <td><?php echo htmlspecialchars($item['suspect_name']); ?></td>
                                            <td><?php echo date('M d, Y h:i A', strtotime($item['hearing_date'])); ?></td>
                                            <td><?php echo htmlspecialchars($item['venue']); ?></td>
                                            <td><?php echo getStatusBadge($item['status']); ?></td>
                                            <td><?php echo htmlspecialchars($item['outcome'] ?? 'Not yet available'); ?></td>
                                            <td><?php echo htmlspecialchars($item['recorded_by_name']); ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                </tbody>
                            </table>
                        <?php elseif ($report_type == 'violation'): ?>
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Case Reference</th>
                                        <th>Suspect Name</th>
                                        <th>Violation Date</th>
                                        <th>Violation Type</th>
                                        <th>Status</th>
                                        <th>Reported By</th>
                                        <th>Actions Taken</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($report_data as $item): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($item['reference_number']); ?></td>
                                            <td><?php echo htmlspecialchars($item['suspect_name']); ?></td>
                                            <td><?php echo date('M d, Y', strtotime($item['violation_date'])); ?></td>
                                            <td><?php echo htmlspecialchars($item['violation_type']); ?></td>
                                            <td><?php echo getStatusBadge($item['status']); ?></td>
                                            <td><?php echo htmlspecialchars($item['reported_by_name']); ?></td>
                                            <td><?php echo htmlspecialchars($item['actions_taken'] ?? 'N/A'); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        <?php endif; ?>
                    <?php endif; ?>
                    </div>
                <div class="card-footer">
                    <p class="text-muted">Report generated on: <?php echo date('F d, Y h:i:s A'); ?> | Total Records: <?php echo count($report_data); ?></p>
                </div>
                </div>
            <?php endif; ?>
        </main>
    </div>
</div>

<script>
function printReport() {
    const reportContent = document.getElementById('reportContent').innerHTML;
    const title = "<?php echo $report_title; ?>";
    const dateRange = "(<?php echo date('M d, Y', strtotime($date_from)); ?> - <?php echo date('M d, Y', strtotime($date_to)); ?>)";
    
    let printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <title>${title}</title>
            <link href="../../assets/css/bootstrap.min.css" rel="stylesheet">
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                .print-header { text-align: center; margin-bottom: 20px; }
                .print-footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
                @media print {
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <div class="print-header">
                <h2><?php echo htmlspecialchars($barangay_name ?? 'Barangay Talisay'); ?></h2>
                <h3>${title} ${dateRange}</h3>
            </div>
            <div>${reportContent}</div>
            <div class="print-footer">
                <p>Generated on: <?php echo date('F d, Y h:i:s A'); ?></p>
            </div>
            <div class="no-print mt-3">
                <button class="btn btn-primary" onclick="window.print()">Print</button>
                <button class="btn btn-secondary" onclick="window.close()">Close</button>
            </div>
        </body>
        </html>
    `);
    printWindow.document.close();
}

function exportToExcel() {
    // Basic Excel export (this can be enhanced with a proper Excel library)
    const reportTable = document.querySelector('#reportContent table');
    if (!reportTable) return;
    
    const title = "<?php echo $report_type; ?>_report_<?php echo date('Y-m-d'); ?>.xls";
    
    // Format the table for Excel
    let html = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">';
    html += '<head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>Report</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--></head>';
    html += '<body>';
    html += reportTable.outerHTML;
    html += '</body></html>';
    
    // Create download link
    const blob = new Blob([html], {type: 'application/vnd.ms-excel'});
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = title;
    link.click();
}
</script>

<?php
include '../../includes/footer.php';
?> 