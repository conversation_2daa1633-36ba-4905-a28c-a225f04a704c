<?php
session_start();
include '../../includes/functions/permission_functions.php';
include '../../includes/config/database.php';
include '../../includes/functions/utility.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission
if (!hasPermission('view_hearings')) {
    header("Location: ../../index.php");
    exit;
}

// Page title
$page_title = "Hearings Management - Barangay Management System";

// Initialize search variables
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$date_filter = isset($_GET['date']) ? $_GET['date'] : '';

// Build search conditions
$search_condition = '';
$status_condition = '';
$date_condition = '';
$params = [];

// Only add search condition if search is not empty
if (!empty($search)) {
    $search_condition = "(c.complaint_type LIKE :search OR 
                         h.hearing_location LIKE :search2 OR
                         c.complainant_name LIKE :search3 OR
                         c.respondent_name LIKE :search4)";
    $params['search'] = "%$search%";
    $params['search2'] = "%$search%";
    $params['search3'] = "%$search%";
    $params['search4'] = "%$search%";
}

if (!empty($status_filter)) {
    $status_condition = "h.status = :status";
    $params['status'] = $status_filter;
}

if (!empty($date_filter)) {
    $date_condition = "DATE(h.hearing_date) = :date";
    $params['date'] = $date_filter;
}

// Build the WHERE clause
$where_clause = [];
$joins = "";

if (!empty($search_condition)) {
    $where_clause[] = $search_condition;
    $joins .= " LEFT JOIN complaints c ON h.complaint_id = c.complaint_id ";
}

if (!empty($status_condition)) {
    $where_clause[] = $status_condition;
}

if (!empty($date_condition)) {
    $where_clause[] = $date_condition;
}

$where_sql = !empty($where_clause) ? "WHERE " . implode(" AND ", $where_clause) : "";

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$records_per_page = 10;
$offset = ($page - 1) * $records_per_page;

// Get total records
$count_query = "SELECT COUNT(*) as total FROM hearings h $joins $where_sql";
try {
    $count_stmt = $conn->prepare($count_query);
    foreach ($params as $key => $value) {
        $count_stmt->bindValue(":$key", $value);
    }
    $count_stmt->execute();
    $total_records = $count_stmt->fetchColumn();
    $total_pages = ceil($total_records / $records_per_page);
} catch (PDOException $e) {
    error_log("Error in count query: " . $e->getMessage());
    $total_records = 0;
    $total_pages = 1;
}

// Get hearings list
$query = "SELECT * FROM hearings ORDER BY hearing_date DESC LIMIT :offset, :per_page";
try {
    $stmt = $conn->prepare($query);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindValue(':per_page', $records_per_page, PDO::PARAM_INT);
    $stmt->execute();
    $hearings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Debug output to error log
    error_log("Main hearings query: " . $query);
    error_log("Hearings found: " . count($hearings));
    
    // Fetch complaint details separately for hearings we have
    if (count($hearings) > 0) {
        $hearing_ids = array_column($hearings, 'hearing_id');
        $placeholders = implode(',', array_fill(0, count($hearing_ids), '?'));
        
        // Simplified query to get just the basic complaint details
        $details_query = "SELECT h.hearing_id, c.complaint_id, c.complaint_type, c.status as complaint_status,
                        c.complainant_name, c.respondent_name
                        FROM hearings h
                        LEFT JOIN complaints c ON h.complaint_id = c.complaint_id
                        WHERE h.hearing_id IN ($placeholders)";
        
        $details_stmt = $conn->prepare($details_query);
        foreach ($hearing_ids as $index => $id) {
            $details_stmt->bindValue($index + 1, $id, PDO::PARAM_INT);
        }
        $details_stmt->execute();
        $details = $details_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Index details by hearing_id for easy lookup
        $details_by_id = [];
        foreach ($details as $detail) {
            $details_by_id[$detail['hearing_id']] = $detail;
        }
        
        // Merge details with hearings
        foreach ($hearings as &$hearing) {
            if (isset($details_by_id[$hearing['hearing_id']])) {
                $hearing = array_merge($hearing, $details_by_id[$hearing['hearing_id']]);
            }
        }
    }
    
    // Debug information
    error_log("Hearings query executed. Records found: " . count($hearings));
    error_log("Raw hearings data: " . print_r($hearings, true));
    
    // Debug output to screen during development
    if (isset($_GET['debug']) && $_GET['debug'] == 1) {
        echo '<div class="alert alert-info">';
        echo '<strong>Debug Info:</strong><br>';
        echo 'Records found: ' . count($hearings) . '<br>';
        echo 'Total from count query: ' . $total_records . '<br>';
        echo 'SQL: ' . htmlspecialchars($query) . '<br>';
        echo '<pre>' . htmlspecialchars(print_r($hearings, true)) . '</pre>';
        echo '</div>';
    }
} catch (PDOException $e) {
    error_log("Error in hearings query: " . $e->getMessage());
    $hearings = [];
}

// Get status counts
$status_query = "SELECT status, COUNT(*) as count FROM hearings GROUP BY status";
try {
    $status_stmt = $conn->prepare($status_query);
    $status_stmt->execute();
    $status_counts = [];
    while ($row = $status_stmt->fetch(PDO::FETCH_ASSOC)) {
        $status_counts[$row['status']] = $row['count'];
    }
} catch (PDOException $e) {
    error_log("Error in status count query: " . $e->getMessage());
    $status_counts = [];
}

// Get upcoming hearings
$upcoming_query = "SELECT h.*, c.complaint_type, c.complaint_id, c.status as complaint_status,
                  c.complainant_name, c.respondent_name
                  FROM hearings h 
                  LEFT JOIN complaints c ON h.complaint_id = c.complaint_id
                  WHERE h.status = 'Scheduled' AND h.hearing_date >= CURDATE()
                  ORDER BY h.hearing_date ASC
                  LIMIT 5";
try {
    $upcoming_stmt = $conn->prepare($upcoming_query);
    $upcoming_stmt->execute();
    $upcoming_hearings = $upcoming_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Log for debugging
    error_log("Upcoming hearings found: " . count($upcoming_hearings));
} catch (PDOException $e) {
    error_log("Error in upcoming hearings query: " . $e->getMessage());
    $upcoming_hearings = [];
}

// Helper function to display hearing status badges
function getHearingStatusBadge($status) {
    switch ($status) {
        case 'Scheduled':
            return '<span class="badge bg-primary">📋 Scheduled</span>';
        case 'Completed':
            return '<span class="badge bg-success">✅ Completed</span>';
        case 'Cancelled':
            return '<span class="badge bg-danger">❌ Cancelled</span>';
        case 'Postponed':
            return '<span class="badge bg-warning">⏳ Postponed</span>';
        default:
            return '<span class="badge bg-secondary">' . $status . '</span>';
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>

    <!-- Favicon -->
    <link rel="icon" href="<?php echo get_favicon_url($conn, '../../'); ?>" type="image/png">

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <style>
        /* Card styling with shadows and border effects */
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: none;
            margin-bottom: 24px;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        
        /* Border left styling */
        .border-left-primary {
            border-left: 4px solid #4e73df;
        }
        
        .border-left-success {
            border-left: 4px solid #1cc88a;
        }
        
        .border-left-info {
            border-left: 4px solid #36b9cc;
        }
        
        .border-left-warning {
            border-left: 4px solid #f6c23e;
        }
        
        .border-left-danger {
            border-left: 4px solid #e74a3b;
        }
        
        /* Card headers with improved colors for better readability */
        .card-header.bg-primary {
            background-color: #0a58ca !important;
            color: white !important;
            font-weight: 600;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            padding: 12px 15px;
            border-radius: 10px 10px 0 0;
        }
        
        .card-header.bg-info {
            background-color: #0dcaf0 !important;
            color: white !important;
            font-weight: 600;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            padding: 12px 15px;
            border-radius: 10px 10px 0 0;
        }
        
        /* Make header text stand out better */
        .card-header h5, 
        .card-header h6 {
            font-size: 1.2rem;
            font-weight: 700;
        }
        
        /* Light backgrounds for form sections */
        .card-body {
            background-color: #fff;
            padding: 20px;
            border-radius: 0 0 10px 10px;
        }
        
        /* Table styling */
        .table {
            border-radius: 8px;
            overflow: hidden;
        }
        
        .table thead th {
            background-color: #f8f9fc;
            border-bottom: 2px solid #e3e6f0;
            font-weight: 600;
        }
        
        .table-hover tbody tr:hover {
            background-color: rgba(78, 115, 223, 0.05);
        }
        
        /* Custom tab styling */
        .nav-tabs .nav-link {
            border: none;
            color: #5a5c69;
            font-weight: 500;
            padding: 10px 15px;
        }
        
        .nav-tabs .nav-link.active {
            color: #4e73df;
            border-bottom: 3px solid #4e73df;
            background-color: transparent;
        }
        
        /* Button styling */
        .btn {
            border-radius: 8px;
            padding: 8px 16px;
            transition: all 0.2s;
            font-weight: 500;
        }
        
        .btn:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        /* Search form styling */
        .search-form {
            border-radius: 30px;
            overflow: hidden;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }
        
        .search-form .form-control {
            border-radius: 8px;
            border: 1px solid #ced4da;
        }
        
        /* List group styling */
        .list-group-item {
            border-left: 3px solid transparent;
            transition: all 0.2s ease;
        }
        
        .list-group-item:hover {
            border-left: 3px solid #4e73df;
            background-color: #f8f9fc;
        }
        
        /* Badge styling */
        .badge {
            font-size: 0.85em;
            padding: 0.5em 0.8em;
            border-radius: 30px;
        }
        
        /* Alert styling */
        .alert {
            border-radius: 10px;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
    <?php include '../../includes/sidebar.php'; ?>
    
            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">📅 Hearings Management</h1>
                    <div>
                        <?php if (hasPermission('schedule_hearing')): ?>
                        <a href="schedule_hearing.php" class="btn btn-primary">
                            🗓️ Schedule New Hearing
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-8">
                        <!-- Status Tabs -->
                        <div class="card border-left-primary shadow mb-4">
                            <div class="card-header bg-white">
                                <ul class="nav nav-tabs card-header-tabs">
                                    <li class="nav-item">
                                        <a class="nav-link <?php echo empty($status_filter) ? 'active' : ''; ?>" href="?">
                                            🗂️ All Hearings <span class="badge bg-secondary"><?php echo $total_records; ?></span>
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link <?php echo $status_filter == 'Scheduled' ? 'active' : ''; ?>" href="?status=Scheduled">
                                            📋 Scheduled <span class="badge bg-primary"><?php echo $status_counts['Scheduled'] ?? 0; ?></span>
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link <?php echo $status_filter == 'Completed' ? 'active' : ''; ?>" href="?status=Completed">
                                            ✅ Completed <span class="badge bg-success"><?php echo $status_counts['Completed'] ?? 0; ?></span>
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link <?php echo $status_filter == 'Postponed' ? 'active' : ''; ?>" href="?status=Postponed">
                                            ⏳ Postponed <span class="badge bg-warning"><?php echo $status_counts['Postponed'] ?? 0; ?></span>
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link <?php echo $status_filter == 'Cancelled' ? 'active' : ''; ?>" href="?status=Cancelled">
                                            ❌ Cancelled <span class="badge bg-danger"><?php echo $status_counts['Cancelled'] ?? 0; ?></span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body">
                                <!-- Search & Filter Row -->
                                <div class="row mb-4">
                                    <div class="col-md-8">
                                        <form action="" method="get" class="d-flex gap-2">
                                            <input type="text" name="search" class="form-control" placeholder="🔎 Search hearings..." value="<?php echo htmlspecialchars($search); ?>">
                                            <input type="date" name="date" class="form-control date-picker" placeholder="📆 Filter by date" value="<?php echo htmlspecialchars($date_filter); ?>">
                                            <?php if (!empty($status_filter)): ?>
                                            <input type="hidden" name="status" value="<?php echo htmlspecialchars($status_filter); ?>">
                                            <?php endif; ?>
                                            <button type="submit" class="btn btn-primary">Search</button>
                                            <a href="hearings.php" class="btn btn-secondary">Reset</a>
                                        </form>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <button class="btn btn-outline-secondary">
                                            🖨️ Print
                                        </button>
                                        <button class="btn btn-outline-success" onclick="exportToExcel()">
                                            📊 Export
                                        </button>
                                    </div>
                                </div>
        
                                <!-- Hearings Table -->
                                <div class="table-responsive">
                                    <table class="table table-hover table-bordered-columns">
                                        <thead class="table-light">
                                            <tr>
                                                <th>🔢 ID</th>
                                                <th>📄 Complaint</th>
                                                <th>👥 Parties</th>
                                                <th>📅 Date & Time</th>
                                                <th>📍 Location</th>
                                                <th>🚦 Status</th>
                                                <th class="action-column">⚙️ Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            // Debug record count
                                            error_log("In table display, hearings count: " . count($hearings));
                                            
                                            if (count($hearings) > 0) {
                                                foreach ($hearings as $hearing) {
                                                    // Get status badge with emoji
                                                    $status_badge = '';
                                                    switch ($hearing['status']) {
                                                        case 'Scheduled':
                                                            $status_badge = '<span class="badge bg-primary">📋 Scheduled</span>';
                                                            break;
                                                        case 'Completed':
                                                            $status_badge = '<span class="badge bg-success">✅ Completed</span>';
                                                            break;
                                                        case 'Cancelled':
                                                            $status_badge = '<span class="badge bg-danger">❌ Cancelled</span>';
                                                            break;
                                                        case 'Postponed':
                                                            $status_badge = '<span class="badge bg-warning">⏳ Postponed</span>';
                                                            break;
                                                        default:
                                                            $status_badge = '<span class="badge bg-secondary">' . $hearing['status'] . '</span>';
                                                    }
                                                
                                                    echo "<tr>";
                                                    echo "<td>" . $hearing['hearing_id'] . "</td>";
                                                    echo "<td><a href='view_complaint.php?id=" . $hearing['complaint_id'] . "' class='link-primary'>#" . $hearing['complaint_id'] . " - " . ($hearing['complaint_type'] ?? 'Complaint') . "</a></td>";
                                                    
                                                    // Improved parties display that handles multiple possible data sources
                                                    $complainant = '';
                                                    $respondent = '';
                                                    
                                                    // Try to get names from resident table first
                                                    if (!empty($hearing['complainant_fname']) && !empty($hearing['complainant_lname'])) {
                                                        $complainant = $hearing['complainant_fname'] . ' ' . $hearing['complainant_lname'];
                                                    }
                                                    // Otherwise try the direct names from complaints table
                                                    else if (!empty($hearing['complainant_name'])) {
                                                        $complainant = $hearing['complainant_name'];
                                                    }
                                                    
                                                    if (!empty($hearing['respondent_fname']) && !empty($hearing['respondent_lname'])) {
                                                        $respondent = $hearing['respondent_fname'] . ' ' . $hearing['respondent_lname'];
                                                    }
                                                    else if (!empty($hearing['respondent_name'])) {
                                                        $respondent = $hearing['respondent_name'];
                                                    }
                                                    
                                                    // Display the parties
                                                    if (!empty($complainant) && !empty($respondent)) {
                                                        echo "<td>" . $complainant . " vs. " . $respondent . "</td>";
                                                    } else if (!empty($complainant)) {
                                                        echo "<td>" . $complainant . "</td>";
                                                    } else if (!empty($respondent)) {
                                                        echo "<td>" . $respondent . "</td>";
                                                    } else {
                                                        echo "<td>N/A</td>";
                                                    }
                                                    
                                                    echo "<td>" . date('M d, Y h:i A', strtotime($hearing['hearing_date'])) . "</td>";
                                                    echo "<td>" . $hearing['hearing_location'] . "</td>";
                                                    echo "<td>" . $status_badge . "</td>";
                                                    echo "<td>";
                                                    echo "<div class='action-buttons'>";
                                                    echo "<a href='view_hearing.php?id=" . $hearing['hearing_id'] . "' class='btn btn-sm btn-info' data-bs-toggle='tooltip' title='View Hearing'><i class='fas fa-eye'></i></a> ";
                                                    
                                                    if (hasPermission('update_hearing') && $hearing['status'] == 'Scheduled') {
                                                        echo "<a href='update_hearing.php?id=" . $hearing['hearing_id'] . "' class='btn btn-sm btn-primary' data-bs-toggle='tooltip' title='Update Hearing'><i class='fas fa-edit'></i></a> ";
                                                    }
                                                    
                                                    if (hasPermission('delete_hearing') && $hearing['status'] == 'Scheduled') {
                                                        echo "<a href='delete_hearing.php?id=" . $hearing['hearing_id'] . "' class='btn btn-sm btn-danger btn-delete' data-bs-toggle='tooltip' title='Cancel Hearing'><i class='fas fa-ban'></i></a>";
                                                    }
                                                    echo "</div>";
                                                    echo "</td>";
                                                    echo "</tr>";
                                                }
                                            } else {
                                                echo "<tr><td colspan='7' class='text-center'>No hearings found</td></tr>";
                                            }
                                            ?>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <!-- Pagination -->
                                <?php
                                $url = "hearings.php";
                                
                                // Initialize the query_params array
                                $query_params = [];
                                
                                if (!empty($search)) {
                                    $query_params[] = "search=" . urlencode($search);
                                }
                                
                                if (!empty($status_filter)) {
                                    $query_params[] = "status=" . urlencode($status_filter);
                                }
                                
                                if (!empty($date_filter)) {
                                    $query_params[] = "date=" . urlencode($date_filter);
                                }
                                
                                if (count($query_params) > 0) {
                                    $url .= "?" . implode("&", $query_params);
                                }
                                
                                echo generatePagination($total_records, $records_per_page, $page, $url);
                                ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <!-- Upcoming Hearings Card -->
                        <div class="card border-left-warning shadow">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="card-title mb-0">⏰ Upcoming Hearings</h5>
                            </div>
                            <div class="card-body">
                                <?php if (count($upcoming_hearings) > 0): ?>
                                <div class="list-group">
                                    <?php foreach ($upcoming_hearings as $upcoming): ?>
                                    <a href="view_hearing.php?id=<?php echo $upcoming['hearing_id']; ?>" class="list-group-item list-group-item-action">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1">📆 <?php echo date('M d, Y h:i A', strtotime($upcoming['hearing_date'])); ?></h6>
                                            <small class="badge bg-secondary"><?php echo htmlspecialchars($upcoming['complaint_type'] ?? 'Complaint'); ?></small>
                                        </div>
                                        <p class="mb-1">
                                            👥 <?php 
                                            $complainant = $upcoming['complainant_name'] ?? 'Complainant';
                                            $respondent = $upcoming['respondent_name'] ?? 'Respondent';
                                            echo htmlspecialchars($complainant . ' vs. ' . $respondent); 
                                            ?>
                                        </p>
                                        <small>📍 <?php echo htmlspecialchars($upcoming['hearing_location']); ?></small>
                                    </a>
                                    <?php endforeach; ?>
                                </div>
                                <?php else: ?>
                                <div class="alert alert-info mb-0">
                                    📝 No upcoming hearings scheduled.
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Calendar Card -->
                        <div class="card border-left-info shadow mt-3">
                            <div class="card-header bg-info text-white">
                                <h5 class="card-title mb-0">🗓️ Hearing Schedule</h5>
                            </div>
                            <div class="card-body">
                                <div id="hearing-calendar"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
            </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="../../assets/js/main.js"></script>
    
    <script>
        // Initialize date picker
        flatpickr(".date-picker", {
            dateFormat: "Y-m-d",
            allowInput: true
        });
        
        // Initialize calendar (placeholder - would be replaced with actual calendar implementation)
        function initCalendar() {
            // This would be replaced with an actual calendar library implementation
            const calendarEl = document.getElementById('hearing-calendar');
            if (calendarEl) {
                calendarEl.innerHTML = '<div class="text-center p-3">Calendar view will be implemented here</div>';
            }
        }
        
        // Function to show toast notifications
        function showToast(message, type = 'success') {
            // Create toast element
            const toast = document.createElement('div');
            toast.className = 'toast align-items-center text-white bg-' + type + ' border-0';
            toast.setAttribute('role', 'alert');
            toast.setAttribute('aria-live', 'assertive');
            toast.setAttribute('aria-atomic', 'true');
            
            // Set position with CSS
            toast.style.position = 'fixed';
            toast.style.bottom = '1rem';
            toast.style.right = '1rem';
            toast.style.zIndex = '9999';
            
            // Create toast content with emoji based on type
            let emoji = '✅';
            if (type === 'danger') emoji = '❌';
            if (type === 'info') emoji = 'ℹ️';
            if (type === 'warning') emoji = '⚠️';
            
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        ${emoji} ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            `;
            
            // Append to body
            document.body.appendChild(toast);
            
            // Initialize and show toast
            const bsToast = new bootstrap.Toast(toast, {
                autohide: true,
                delay: 5000
            });
            bsToast.show();
            
            // Remove from DOM after hidden
            toast.addEventListener('hidden.bs.toast', function () {
                document.body.removeChild(toast);
            });
        }
        
        // Check for session messages
        document.addEventListener('DOMContentLoaded', function() {
            <?php if(isset($_SESSION['success'])): ?>
                showToast("<?php echo $_SESSION['success']; ?>", "success");
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>
            
            <?php if(isset($_SESSION['error'])): ?>
                showToast("<?php echo $_SESSION['error']; ?>", "danger");
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>
            
            <?php if(isset($_SESSION['info'])): ?>
                showToast("<?php echo $_SESSION['info']; ?>", "info");
                <?php unset($_SESSION['info']); ?>
            <?php endif; ?>
        });
        
        $(document).ready(function() {
            initCalendar();
        });
        
        function exportToExcel() {
            // Placeholder for Excel export functionality
            alert('Export to Excel functionality will be implemented here');
        }
        
        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            });
        });
    </script>
</body>
</html>